export interface YearlyData {
  previousYear: number;
  currentYear: number;
  projectedYear: number;
}

export interface CompanyInfo {
  id?: string;
  companyName: string;
  address: string;
  contactPerson: string;
  phone: string;
  email: string;
  panNumber: string;
  gstNumber: string;
  numberOfYears: number;
  createdAt?: Date;
}

export interface SingleYearData {
  [key: string]: number;
}

export interface BalanceSheetYear {
  year: string;
  yearType:
    | "Previous Year (Audited)"
    | "Current Year (Actual)"
    | "Projected Next Year";
  liabilities: {
    currentLiabilities: SingleYearData;
    branchSisterConcerns: SingleYearData;
    longTermLiabilities: SingleYearData;
    capitalAndReserve: SingleYearData;
  };
  assets: {
    currentAssets: SingleYearData;
    fixedAssets: SingleYearData;
    nonCurrentAssets: SingleYearData;
    others: SingleYearData;
  };
  profitLoss: SingleYearData;
}

export interface BalanceSheetTotals {
  totalAssets: number;
  totalLiabilities: number;
  year: string;
}

export type AnalysisChoice =
  | "analyze-existing"
  | "upload-analysis"
  | "create-new";

export interface CurrentLiabilities {
  tradeCreditors: YearlyData;
  expensesDutyOtherPayables: YearlyData;
  lcCreditorsJobWork: YearlyData;
  advanceReceivedFromBuyers: YearlyData;
  shortTermBankFinance: YearlyData;
  provisions: YearlyData;
  taxPayable: YearlyData;
  commercialPaper: YearlyData;
  sdMaturityRepayableOneYear: YearlyData;
}

export interface BranchSisterConcerns {
  havingCreditFacilities: YearlyData;
  notHavingFacilities: YearlyData;
}

export interface LongTermLiabilities {
  longTermFundsFromFamily: YearlyData;
  longTermLoanFromBank: YearlyData;
  longTermLoanFromInstitutions: YearlyData;
  cautionDeposit: YearlyData;
}

export interface CapitalAndReserve {
  paidUpCapital: YearlyData;
  reservesAndSurplus: YearlyData;
  convertiblePreferenceShare: YearlyData;
  plAccount: YearlyData;
  lessMiscExpensesDrawings: YearlyData;
}

export interface CurrentAssets {
  cashAndBankBalance: YearlyData;
  stock: YearlyData;
  receivables: YearlyData;
  shortTermAdvance: YearlyData;
  otherCurrentAsset: YearlyData;
  depositAdvances: YearlyData;
  others: YearlyData;
}

export interface FixedAssets {
  landAndBuilding: YearlyData;
  plantMachinery: YearlyData;
  fittingsFurniture: YearlyData;
  vehicles: YearlyData;
  officeEquipments: YearlyData;
  advanceAgainstCapitalGoods: YearlyData;
}

export interface NonCurrentAssets {
  investment: YearlyData;
  deferredTaxOthers: YearlyData;
}

export interface Others {
  otherAdvance: YearlyData;
  buildingDepositOtherDeposit: YearlyData;
  inventoryProject: YearlyData;
}

export interface BalanceSheetData {
  currentLiabilities: CurrentLiabilities;
  branchSisterConcerns: BranchSisterConcerns;
  longTermLiabilities: LongTermLiabilities;
  capitalAndReserve: CapitalAndReserve;
  currentAssets: CurrentAssets;
  fixedAssets: FixedAssets;
  nonCurrentAssets: NonCurrentAssets;
  others: Others;
}

export interface ProfitLossData {
  grossSalesDomestic: YearlyData;
  exportSales: YearlyData;
  commissionSales: YearlyData;
  interest: YearlyData;
  tax: YearlyData;
  depreciation: YearlyData;
  netProfit: YearlyData;
}

export interface AnalysisResults {
  plAnalysis: {
    pbdit: YearlyData;
    pbit: YearlyData;
    cashProfit: YearlyData;
  };
  ratioAnalysis: {
    currentRatio: YearlyData;
    tolQeToTnw: YearlyData;
    tolToTnwQe: YearlyData;
    netWorkingCapital: YearlyData;
    velocityOfTurnover: YearlyData;
    salesToTnw: YearlyData;
    tradeDebtorsOutstanding: YearlyData;
    tradeCreditorsOutstanding: YearlyData;
    closingStockHeld: YearlyData;
  };
  workingCapitalAssessment: {
    estimatedSalesTurnover: number;
    totalCurrentAssets: number;
    currentLiabilitiesOtherThanBank: number;
    workingCapitalGap: number;
    marginHigher: number;
    maxPermissibleBankFinance: number;
    marginMoneyRequirement: {
      workingCapitalGap: number;
      bankFinance: number;
      marginRequired: number;
    };
    availabilityOfMarginMoney: {
      availableNwcCurrentYear: number;
      increaseInCapital: number;
      increaseInQuasiEquity: number;
      increaseInOtherLongTermLoans: number;
      depreciation: number;
      increaseDecreaseInNonCurrentAssets: number;
      increaseDecreaseInLendingToAssociates: number;
      increaseDecreaseInFixedAssets: number;
      availableMarginMoney: number;
    };
  };
}
