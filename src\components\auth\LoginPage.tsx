import React, { useState } from "react";
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  BarChart3,
  Shield,
  CheckCircle,
} from "lucide-react";
import { LoginCredentials, TEST_USER_CREDENTIALS } from "../../types/Auth";
import "./AuthPages.css";

interface Props {
  onLogin: (credentials: LoginCredentials) => Promise<void>;
  onRegister: () => void;
  onForgotPassword: () => void;
  onBackToLanding: () => void;
  isLoading?: boolean;
  error?: string;
}

const LoginPage: React.FC<Props> = ({
  onLogin,
  onRegister,
  onForgotPassword,
  onBackToLanding,
  isLoading = false,
  error,
}) => {
  const [formData, setFormData] = useState<LoginCredentials>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onLogin(formData);
  };

  const handleInputChange = (field: keyof LoginCredentials, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const fillTestCredentials = () => {
    setFormData({
      email: TEST_USER_CREDENTIALS.email,
      password: TEST_USER_CREDENTIALS.password,
    });
  };

  return (
    <div className="auth-page">
      <div className="auth-container">
        {/* Left Side - Branding */}
        <div className="auth-branding">
          <div className="branding-content">
            <div className="brand-logo">
              <BarChart3 className="brand-icon" />
              <span className="brand-name">FinanceAI Pro</span>
            </div>
            <h1 className="branding-title">
              Welcome Back to the Future of Financial Analysis
            </h1>
            <p className="branding-description">
              Join thousands of professionals who trust our AI-powered platform
              for accurate, fast financial insights and analysis.
            </p>

            <div className="branding-features">
              <div className="branding-feature">
                <CheckCircle className="feature-check" />
                <span>90%+ Accuracy Rate</span>
              </div>
              <div className="branding-feature">
                <CheckCircle className="feature-check" />
                <span>Instant AI Analysis</span>
              </div>
              <div className="branding-feature">
                <CheckCircle className="feature-check" />
                <span>Bank-Grade Security</span>
              </div>
            </div>

            <div className="security-badge">
              <Shield className="security-icon" />
              <div className="security-text">
                <span className="security-title">Enterprise Security</span>
                <span className="security-subtitle">
                  Your data is protected with 256-bit encryption
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="auth-form-section">
          <div className="auth-form-container">
            <div className="auth-header">
              <button onClick={onBackToLanding} className="back-to-landing">
                ← Back to Home
              </button>
              <h2 className="auth-title">Sign In</h2>
              <p className="auth-subtitle">
                Access your dashboard and continue your financial analysis
              </p>
            </div>

            {error && (
              <div className="auth-error">
                <span>{error}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="auth-form">
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  Email Address
                </label>
                <div className="input-wrapper">
                  <Mail className="input-icon" />
                  <input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="Enter your email"
                    className="form-input"
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Password
                </label>
                <div className="input-wrapper">
                  <Lock className="input-icon" />
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    placeholder="Enter your password"
                    className="form-input"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              <div className="form-options">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="checkbox-input"
                  />
                  <span className="checkbox-text">Remember me</span>
                </label>
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="forgot-password"
                >
                  Forgot password?
                </button>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="auth-submit-btn"
              >
                {isLoading ? (
                  <span className="loading-spinner"></span>
                ) : (
                  <>
                    Sign In
                    <ArrowRight className="btn-icon" />
                  </>
                )}
              </button>
            </form>

            <div className="auth-divider">
              <span>or</span>
            </div>

            <div className="test-user-info">
              <div className="test-user-card">
                <h4 className="test-user-title">🧪 Test Account</h4>
                <p className="test-user-desc">
                  Use these credentials for full site access:
                </p>
                <div className="test-credentials">
                  <div className="credential-item">
                    <span className="credential-label">Username:</span>
                    <code className="credential-value">vivek.s.rajiv</code>
                  </div>
                  <div className="credential-item">
                    <span className="credential-label">Password:</span>
                    <code className="credential-value">Tester@Wholesite</code>
                  </div>
                </div>
                <p className="test-user-features">
                  ✨ Unlimited access to all features
                </p>
                <button
                  type="button"
                  onClick={fillTestCredentials}
                  className="test-fill-btn"
                >
                  🚀 Auto-Fill Test Credentials
                </button>
              </div>
            </div>

            <div className="auth-alternative">
              <p className="alternative-text">Don't have an account?</p>
              <button onClick={onRegister} className="alternative-btn">
                Create Account
              </button>
            </div>

            <div className="auth-footer">
              <p className="footer-text">
                By signing in, you agree to our{" "}
                <a href="#terms" className="footer-link">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#privacy" className="footer-link">
                  Privacy Policy
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
