import { supabase } from "../lib/supabase";
import { User, LoginCredentials, RegisterData } from "../types/Auth";
import { CompanyInfo, BalanceSheetYear } from "../types/FinancialData";

// Authentication Services
export class AuthService {
  static async signUp(
    data: RegisterData
  ): Promise<{ user: User | null; error: string | null }> {
    try {
      // Sign up with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            first_name: data.firstName,
            last_name: data.lastName,
            company: data.company,
            phone: data.phone,
          },
        },
      });

      if (authError) {
        return { user: null, error: authError.message };
      }

      if (!authData.user) {
        return { user: null, error: "Failed to create user" };
      }

      // Create user profile in our users table
      const { error: profileError } = await supabase.from("users").insert({
        id: authData.user.id,
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        company: data.company,
        phone: data.phone,
        subscription_type: "free",
        subscription_status: "active",
        max_usage: 1, // Free tier gets 1 analysis
      });

      if (profileError) {
        console.error("Profile creation error:", profileError);
        return { user: null, error: "Failed to create user profile" };
      }

      // Convert to our User type
      const user: User = {
        id: authData.user.id,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: "user",
        subscription: {
          type: "free",
          status: "active",
          startDate: new Date(),
          features: {
            maxAnalyses: 1,
            maxCompanies: 1,
            exportToExcel: false,
            prioritySupport: false,
            advancedAnalytics: false,
            apiAccess: false,
            customReports: false,
          },
          usageCount: 0,
          maxUsage: 1,
        },
        isEmailVerified: false,
        createdAt: new Date(),
        company: data.company,
        phone: data.phone,
      };

      return { user, error: null };
    } catch (error) {
      console.error("Sign up error:", error);
      return { user: null, error: "An unexpected error occurred" };
    }
  }

  static async signIn(
    credentials: LoginCredentials
  ): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email: credentials.email,
          password: credentials.password,
        });

      if (authError) {
        return { user: null, error: authError.message };
      }

      if (!authData.user) {
        return { user: null, error: "Failed to sign in" };
      }

      // Get user profile from our users table
      const { data: profile, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", authData.user.id)
        .single();

      if (profileError || !profile) {
        console.error("Profile fetch error:", profileError);
        return { user: null, error: "Failed to load user profile" };
      }

      // Update last login
      await supabase
        .from("users")
        .update({ last_login_at: new Date().toISOString() })
        .eq("id", authData.user.id);

      // Convert to our User type
      const user: User = {
        id: profile.id,
        email: profile.email,
        firstName: profile.first_name,
        lastName: profile.last_name,
        role: profile.role,
        subscription: {
          type: profile.subscription_type,
          status: profile.subscription_status,
          startDate: new Date(profile.subscription_start_date),
          endDate: profile.subscription_end_date
            ? new Date(profile.subscription_end_date)
            : undefined,
          features: {
            maxAnalyses: profile.max_usage,
            maxCompanies: profile.subscription_type === "free" ? 1 : -1,
            exportToExcel: profile.subscription_type !== "free",
            prioritySupport:
              profile.subscription_type === "premium" ||
              profile.subscription_type === "lifetime",
            advancedAnalytics: profile.subscription_type !== "free",
            apiAccess: profile.subscription_type === "lifetime",
            customReports: profile.subscription_type !== "free",
          },
          usageCount: profile.usage_count,
          maxUsage: profile.max_usage,
        },
        isEmailVerified: profile.is_email_verified,
        createdAt: new Date(profile.created_at),
        lastLoginAt: profile.last_login_at
          ? new Date(profile.last_login_at)
          : undefined,
        profileImage: profile.profile_image,
        company: profile.company,
        phone: profile.phone,
      };

      return { user, error: null };
    } catch (error) {
      console.error("Sign in error:", error);
      return { user: null, error: "An unexpected error occurred" };
    }
  }

  static async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      return { error: error?.message || null };
    } catch (error) {
      console.error("Sign out error:", error);
      return { error: "An unexpected error occurred" };
    }
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      const {
        data: { user: authUser },
      } = await supabase.auth.getUser();

      if (!authUser) return null;

      const { data: profile, error } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single();

      if (error || !profile) {
        console.error("Profile fetch error:", error);
        return null;
      }

      return {
        id: profile.id,
        email: profile.email,
        firstName: profile.first_name,
        lastName: profile.last_name,
        role: profile.role,
        subscription: {
          type: profile.subscription_type,
          status: profile.subscription_status,
          startDate: new Date(profile.subscription_start_date),
          endDate: profile.subscription_end_date
            ? new Date(profile.subscription_end_date)
            : undefined,
          features: {
            maxAnalyses: profile.max_usage,
            maxCompanies: profile.subscription_type === "free" ? 1 : -1,
            exportToExcel: profile.subscription_type !== "free",
            prioritySupport:
              profile.subscription_type === "premium" ||
              profile.subscription_type === "lifetime",
            advancedAnalytics: profile.subscription_type !== "free",
            apiAccess: profile.subscription_type === "lifetime",
            customReports: profile.subscription_type !== "free",
          },
          usageCount: profile.usage_count,
          maxUsage: profile.max_usage,
        },
        isEmailVerified: profile.is_email_verified,
        createdAt: new Date(profile.created_at),
        lastLoginAt: profile.last_login_at
          ? new Date(profile.last_login_at)
          : undefined,
        profileImage: profile.profile_image,
        company: profile.company,
        phone: profile.phone,
      };
    } catch (error) {
      console.error("Get current user error:", error);
      return null;
    }
  }
}

// Company Services
export class CompanyService {
  static async createCompany(
    companyData: Omit<CompanyInfo, "id" | "createdAt">
  ): Promise<{ company: CompanyInfo | null; error: string | null }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { company: null, error: "User not authenticated" };
      }

      const { data, error } = await supabase
        .from("companies")
        .insert({
          user_id: user.id,
          company_name: companyData.companyName,
          address: companyData.address,
          contact_person: companyData.contactPerson,
          phone: companyData.phone,
          email: companyData.email,
          pan_number: companyData.panNumber,
          gst_number: companyData.gstNumber,
          number_of_years: companyData.numberOfYears,
        })
        .select()
        .single();

      if (error) {
        console.error("Company creation error:", error);
        return { company: null, error: "Failed to create company" };
      }

      const company: CompanyInfo = {
        id: data.id,
        companyName: data.company_name,
        address: data.address,
        contactPerson: data.contact_person,
        phone: data.phone,
        email: data.email,
        panNumber: data.pan_number,
        gstNumber: data.gst_number,
        numberOfYears: data.number_of_years,
        createdAt: new Date(data.created_at),
      };

      return { company, error: null };
    } catch (error) {
      console.error("Create company error:", error);
      return { company: null, error: "An unexpected error occurred" };
    }
  }

  static async getUserCompanies(): Promise<{
    companies: CompanyInfo[];
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { companies: [], error: "User not authenticated" };
      }

      const { data, error } = await supabase
        .from("companies")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Fetch companies error:", error);
        return { companies: [], error: "Failed to fetch companies" };
      }

      const companies: CompanyInfo[] = data.map((company) => ({
        id: company.id,
        companyName: company.company_name,
        address: company.address,
        contactPerson: company.contact_person,
        phone: company.phone,
        email: company.email,
        panNumber: company.pan_number,
        gstNumber: company.gst_number,
        numberOfYears: company.number_of_years,
        createdAt: new Date(company.created_at),
      }));

      return { companies, error: null };
    } catch (error) {
      console.error("Get user companies error:", error);
      return { companies: [], error: "An unexpected error occurred" };
    }
  }
}

// Balance Sheet Analysis Services
export class AnalysisService {
  static async createAnalysis(
    companyId: string,
    analysisName: string,
    analysisType: "manual" | "upload" | "generated"
  ): Promise<{ analysisId: string | null; error: string | null }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { analysisId: null, error: "User not authenticated" };
      }

      // Check usage limits
      const { data: userProfile } = await supabase
        .from("users")
        .select("usage_count, max_usage")
        .eq("id", user.id)
        .single();

      if (userProfile && userProfile.usage_count >= userProfile.max_usage) {
        return {
          analysisId: null,
          error: "Usage limit exceeded. Please upgrade your subscription.",
        };
      }

      const { data, error } = await supabase
        .from("balance_sheet_analyses")
        .insert({
          user_id: user.id,
          company_id: companyId,
          analysis_name: analysisName,
          analysis_type: analysisType,
        })
        .select()
        .single();

      if (error) {
        console.error("Analysis creation error:", error);
        return { analysisId: null, error: "Failed to create analysis" };
      }

      // Increment usage count
      await supabase
        .from("users")
        .update({ usage_count: (userProfile?.usage_count || 0) + 1 })
        .eq("id", user.id);

      return { analysisId: data.id, error: null };
    } catch (error) {
      console.error("Create analysis error:", error);
      return { analysisId: null, error: "An unexpected error occurred" };
    }
  }

  static async saveBalanceSheetData(
    analysisId: string,
    yearData: BalanceSheetYear
  ): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.from("balance_sheet_data").upsert({
        analysis_id: analysisId,
        year: yearData.year,
        year_type: yearData.yearType,
        liabilities_data: yearData.liabilities,
        assets_data: yearData.assets,
        profit_loss_data: yearData.profitLoss,
      });

      if (error) {
        console.error("Save balance sheet data error:", error);
        return { error: "Failed to save balance sheet data" };
      }

      return { error: null };
    } catch (error) {
      console.error("Save balance sheet data error:", error);
      return { error: "An unexpected error occurred" };
    }
  }

  static async getAnalysisData(
    analysisId: string
  ): Promise<{ data: BalanceSheetYear[]; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from("balance_sheet_data")
        .select("*")
        .eq("analysis_id", analysisId)
        .order("year");

      if (error) {
        console.error("Get analysis data error:", error);
        return { data: [], error: "Failed to fetch analysis data" };
      }

      const balanceSheetData: BalanceSheetYear[] = data.map((item) => ({
        year: item.year,
        yearType: item.year_type,
        liabilities: item.liabilities_data,
        assets: item.assets_data,
        profitLoss: item.profit_loss_data,
      }));

      return { data: balanceSheetData, error: null };
    } catch (error) {
      console.error("Get analysis data error:", error);
      return { data: [], error: "An unexpected error occurred" };
    }
  }

  static async getUserAnalyses(): Promise<{
    analyses: any[];
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { analyses: [], error: "User not authenticated" };
      }

      const { data, error } = await supabase
        .from("balance_sheet_analyses")
        .select(
          `
          *,
          companies (
            company_name
          )
        `
        )
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Get user analyses error:", error);
        return { analyses: [], error: "Failed to fetch analyses" };
      }

      return { analyses: data || [], error: null };
    } catch (error) {
      console.error("Get user analyses error:", error);
      return { analyses: [], error: "An unexpected error occurred" };
    }
  }

  static async completeAnalysis(
    analysisId: string
  ): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase
        .from("balance_sheet_analyses")
        .update({ status: "completed" })
        .eq("id", analysisId);

      if (error) {
        console.error("Complete analysis error:", error);
        return { error: "Failed to complete analysis" };
      }

      return { error: null };
    } catch (error) {
      console.error("Complete analysis error:", error);
      return { error: "An unexpected error occurred" };
    }
  }
}

// File Upload Services
export class FileService {
  static async uploadFile(
    file: File,
    analysisId: string
  ): Promise<{ filePath: string | null; error: string | null }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { filePath: null, error: "User not authenticated" };
      }

      // Create unique file path
      const fileExt = file.name.split(".").pop();
      const fileName = `${user.id}/${analysisId}/${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("balance-sheet-uploads")
        .upload(fileName, file);

      if (uploadError) {
        console.error("File upload error:", uploadError);
        return { filePath: null, error: "Failed to upload file" };
      }

      // Save file record to database
      const { error: dbError } = await supabase.from("uploaded_files").insert({
        user_id: user.id,
        analysis_id: analysisId,
        file_name: file.name,
        file_path: uploadData.path,
        file_size: file.size,
        file_type: file.type,
      });

      if (dbError) {
        console.error("File record creation error:", dbError);
        return { filePath: null, error: "Failed to save file record" };
      }

      return { filePath: uploadData.path, error: null };
    } catch (error) {
      console.error("Upload file error:", error);
      return { filePath: null, error: "An unexpected error occurred" };
    }
  }

  static async getFileUrl(
    filePath: string
  ): Promise<{ url: string | null; error: string | null }> {
    try {
      const { data, error } = await supabase.storage
        .from("balance-sheet-uploads")
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (error) {
        console.error("Get file URL error:", error);
        return { url: null, error: "Failed to get file URL" };
      }

      return { url: data.signedUrl, error: null };
    } catch (error) {
      console.error("Get file URL error:", error);
      return { url: null, error: "An unexpected error occurred" };
    }
  }
}

// Activity Logging Service
export class ActivityService {
  static async logActivity(
    activityType: string,
    activityData: any = {}
  ): Promise<void> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) return;

      await supabase.from("user_activity_log").insert({
        user_id: user.id,
        activity_type: activityType,
        activity_data: activityData,
      });
    } catch (error) {
      console.error("Log activity error:", error);
      // Don't throw error for logging failures
    }
  }
}
