import React from "react";
import { ProfitLossData, YearlyData } from "../types/FinancialData";
import "./ProfitLossForm.css";

interface Props {
  data: ProfitLossData;
  onChange: (data: ProfitLossData) => void;
}

const ProfitLossForm: React.FC<Props> = ({ data, onChange }) => {
  const updateField = (
    field: keyof ProfitLossData,
    year: keyof YearlyData,
    value: number
  ) => {
    const newData = { ...data };
    newData[field][year] = value;
    onChange(newData);
  };

  const renderYearlyInputs = (
    field: keyof ProfitLossData,
    label: string,
    yearlyData: YearlyData
  ) => (
    <tr key={field}>
      <td className="field-label">{label}</td>
      <td>
        <input
          type="number"
          value={yearlyData.previousYear || ""}
          onChange={(e) =>
            updateField(field, "previousYear", parseFloat(e.target.value) || 0)
          }
          placeholder="0"
        />
      </td>
      <td>
        <input
          type="number"
          value={yearlyData.currentYear || ""}
          onChange={(e) =>
            updateField(field, "currentYear", parseFloat(e.target.value) || 0)
          }
          placeholder="0"
        />
      </td>
      <td>
        <input
          type="number"
          value={yearlyData.projectedYear || ""}
          onChange={(e) =>
            updateField(field, "projectedYear", parseFloat(e.target.value) || 0)
          }
          placeholder="0"
        />
      </td>
    </tr>
  );

  const calculateSalesTotal = (): YearlyData => {
    const total: YearlyData = {
      previousYear: 0,
      currentYear: 0,
      projectedYear: 0,
    };
    // Only include sales-related items, not interest, tax, or net profit
    total.previousYear =
      (data.grossSalesDomestic.previousYear || 0) +
      (data.exportSales.previousYear || 0) +
      (data.commissionSales.previousYear || 0);
    total.currentYear =
      (data.grossSalesDomestic.currentYear || 0) +
      (data.exportSales.currentYear || 0) +
      (data.commissionSales.currentYear || 0);
    total.projectedYear =
      (data.grossSalesDomestic.projectedYear || 0) +
      (data.exportSales.projectedYear || 0) +
      (data.commissionSales.projectedYear || 0);
    return total;
  };

  const renderTotal = (label: string, total: YearlyData) => (
    <tr className="section-total">
      <td className="field-label">
        <strong>{label}</strong>
      </td>
      <td>
        <strong>{total.previousYear.toLocaleString()}</strong>
      </td>
      <td>
        <strong>{total.currentYear.toLocaleString()}</strong>
      </td>
      <td>
        <strong>{total.projectedYear.toLocaleString()}</strong>
      </td>
    </tr>
  );

  return (
    <div className="profit-loss-form">
      <h2>Profit & Loss Data Entry</h2>

      <table className="financial-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Previous Year (Audited)</th>
            <th>Current Year (Actual)</th>
            <th>Projected Next Year</th>
          </tr>
        </thead>
        <tbody>
          <tr className="section-header">
            <td colSpan={4}>
              <strong>Profit and Loss Details</strong>
            </td>
          </tr>

          {renderYearlyInputs(
            "grossSalesDomestic",
            "Gross Sales/Domestic Sales",
            data.grossSalesDomestic
          )}
          {renderYearlyInputs("exportSales", "Export Sales", data.exportSales)}
          {renderYearlyInputs(
            "commissionSales",
            "Commission Sales",
            data.commissionSales
          )}
          {renderYearlyInputs("interest", "Interest", data.interest)}
          {renderYearlyInputs("tax", "Tax", data.tax)}
          {renderYearlyInputs(
            "depreciation",
            "Depreciation",
            data.depreciation
          )}
          {renderYearlyInputs("netProfit", "Net Profit", data.netProfit)}

          {renderTotal("Total Sales/Receipts", calculateSalesTotal())}
        </tbody>
      </table>
    </div>
  );
};

export default ProfitLossForm;
