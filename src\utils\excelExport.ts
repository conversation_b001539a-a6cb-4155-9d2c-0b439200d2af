import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { BalanceSheetYear, CompanyInfo } from "../types/FinancialData";

export interface ExcelExportData {
  companyInfo: CompanyInfo;
  extractedData: BalanceSheetYear[];
  analysisResults?: any;
}

export const exportToExcel = (data: ExcelExportData): void => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // 1. Company Information Sheet
    const companySheet = createCompanyInfoSheet(data.companyInfo);
    XLSX.utils.book_append_sheet(workbook, companySheet, "Company Info");

    // 2. Raw Extracted Data Sheet
    if (data.extractedData.length > 0) {
      const rawDataSheet = createRawDataSheet(data.extractedData);
      XLSX.utils.book_append_sheet(workbook, rawDataSheet, "Extracted Data");
    }

    // 3. Balance Sheet Summary
    if (data.extractedData.length > 0) {
      const balanceSheetSheet = createBalanceSheetSummary(data.extractedData);
      XLSX.utils.book_append_sheet(
        workbook,
        balanceSheetSheet,
        "Balance Sheet"
      );
    }

    // 4. P&L Summary
    if (data.extractedData.length > 0) {
      const plSheet = createPLSummary(data.extractedData);
      XLSX.utils.book_append_sheet(workbook, plSheet, "Profit & Loss");
    }

    // 5. Financial Ratios (if analysis results available)
    if (data.analysisResults) {
      const ratiosSheet = createRatiosSheet(data.analysisResults);
      XLSX.utils.book_append_sheet(workbook, ratiosSheet, "Financial Ratios");
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `${data.companyInfo.companyName.replace(
      /[^a-zA-Z0-9]/g,
      "_"
    )}_Financial_Analysis_${timestamp}.xlsx`;

    // Write and download the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, filename);
  } catch (error) {
    console.error("Error exporting to Excel:", error);
    throw new Error("Failed to export data to Excel. Please try again.");
  }
};

const createCompanyInfoSheet = (companyInfo: CompanyInfo): XLSX.WorkSheet => {
  const data = [
    ["Company Information", ""],
    ["", ""],
    ["Company Name", companyInfo.companyName],
    ["Address", companyInfo.address],
    ["Contact Person", companyInfo.contactPerson],
    ["Phone", companyInfo.phone],
    ["Email", companyInfo.email],
    ["PAN Number", companyInfo.panNumber],
    ["GST Number", companyInfo.gstNumber],
    ["Analysis Period", `${companyInfo.numberOfYears} Years`],
    ["Report Generated", new Date().toLocaleDateString()],
    ["", ""],
    ["Generated by", "FinanceAI Pro - AI-Powered Financial Analysis"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);

  // Set column widths
  worksheet["!cols"] = [{ width: 20 }, { width: 40 }];

  // Style the header
  if (worksheet["A1"]) {
    worksheet["A1"].s = {
      font: { bold: true, size: 14 },
      fill: { fgColor: { rgb: "3182CE" } },
      font: { color: { rgb: "FFFFFF" } },
    };
  }

  return worksheet;
};

const createRawDataSheet = (
  extractedData: BalanceSheetYear[]
): XLSX.WorkSheet => {
  const headers = [
    "Category",
    "Item",
    ...extractedData.map((d) => `${d.year} (${d.yearType})`),
  ];
  const rows: any[][] = [headers];

  // Add liabilities data
  if (extractedData.length > 0) {
    const sampleData = extractedData[0];

    // Current Liabilities
    rows.push(["LIABILITIES", "", ...Array(extractedData.length).fill("")]);
    rows.push([
      "Current Liabilities",
      "",
      ...Array(extractedData.length).fill(""),
    ]);

    Object.keys(sampleData.liabilities.currentLiabilities).forEach((key) => {
      const values = extractedData.map(
        (d) => d.liabilities.currentLiabilities[key] || 0
      );
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Branch/Sister Concerns
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push([
      "Branch/Sister Concerns",
      "",
      ...Array(extractedData.length).fill(""),
    ]);
    Object.keys(sampleData.liabilities.branchSisterConcerns).forEach((key) => {
      const values = extractedData.map(
        (d) => d.liabilities.branchSisterConcerns[key] || 0
      );
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Long Term Liabilities
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push([
      "Long Term Liabilities",
      "",
      ...Array(extractedData.length).fill(""),
    ]);
    Object.keys(sampleData.liabilities.longTermLiabilities).forEach((key) => {
      const values = extractedData.map(
        (d) => d.liabilities.longTermLiabilities[key] || 0
      );
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Capital & Reserve
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push([
      "Capital & Reserve",
      "",
      ...Array(extractedData.length).fill(""),
    ]);
    Object.keys(sampleData.liabilities.capitalAndReserve).forEach((key) => {
      const values = extractedData.map(
        (d) => d.liabilities.capitalAndReserve[key] || 0
      );
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Assets
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push(["ASSETS", "", ...Array(extractedData.length).fill("")]);

    // Current Assets
    rows.push(["Current Assets", "", ...Array(extractedData.length).fill("")]);
    Object.keys(sampleData.assets.currentAssets).forEach((key) => {
      const values = extractedData.map((d) => d.assets.currentAssets[key] || 0);
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Fixed Assets
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push(["Fixed Assets", "", ...Array(extractedData.length).fill("")]);
    Object.keys(sampleData.assets.fixedAssets).forEach((key) => {
      const values = extractedData.map((d) => d.assets.fixedAssets[key] || 0);
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Non Current Assets
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push([
      "Non Current Assets",
      "",
      ...Array(extractedData.length).fill(""),
    ]);
    Object.keys(sampleData.assets.nonCurrentAssets).forEach((key) => {
      const values = extractedData.map(
        (d) => d.assets.nonCurrentAssets[key] || 0
      );
      rows.push(["", formatFieldName(key), ...values]);
    });

    // Others
    rows.push(["", "", ...Array(extractedData.length).fill("")]);
    rows.push(["Others", "", ...Array(extractedData.length).fill("")]);
    Object.keys(sampleData.assets.others).forEach((key) => {
      const values = extractedData.map((d) => d.assets.others[key] || 0);
      rows.push(["", formatFieldName(key), ...values]);
    });
  }

  const worksheet = XLSX.utils.aoa_to_sheet(rows);

  // Set column widths
  worksheet["!cols"] = [
    { width: 20 },
    { width: 30 },
    ...extractedData.map(() => ({ width: 15 })),
  ];

  return worksheet;
};

const createBalanceSheetSummary = (
  extractedData: BalanceSheetYear[]
): XLSX.WorkSheet => {
  const headers = [
    "Item",
    ...extractedData.map((d) => `${d.year} (${d.yearType})`),
  ];
  const rows: any[][] = [headers];

  if (extractedData.length > 0) {
    // Calculate totals for each year
    const totalCurrentLiabilities = extractedData.map((d) =>
      Object.values(d.liabilities.currentLiabilities).reduce(
        (sum, val) => sum + (val || 0),
        0
      )
    );

    const totalLongTermLiabilities = extractedData.map((d) =>
      Object.values(d.liabilities.longTermLiabilities).reduce(
        (sum, val) => sum + (val || 0),
        0
      )
    );

    const totalCapitalReserve = extractedData.map((d) =>
      Object.values(d.liabilities.capitalAndReserve).reduce(
        (sum, val) => sum + (val || 0),
        0
      )
    );

    const totalCurrentAssets = extractedData.map((d) =>
      Object.values(d.assets.currentAssets).reduce(
        (sum, val) => sum + (val || 0),
        0
      )
    );

    const totalFixedAssets = extractedData.map((d) =>
      Object.values(d.assets.fixedAssets).reduce(
        (sum, val) => sum + (val || 0),
        0
      )
    );

    // Add summary rows
    rows.push(["LIABILITIES", ...Array(extractedData.length).fill("")]);
    rows.push(["Current Liabilities", ...totalCurrentLiabilities]);
    rows.push(["Long Term Liabilities", ...totalLongTermLiabilities]);
    rows.push(["Capital & Reserve", ...totalCapitalReserve]);
    rows.push([
      "Total Liabilities",
      ...extractedData.map(
        (_, i) =>
          totalCurrentLiabilities[i] +
          totalLongTermLiabilities[i] +
          totalCapitalReserve[i]
      ),
    ]);

    rows.push(["", ...Array(extractedData.length).fill("")]);
    rows.push(["ASSETS", ...Array(extractedData.length).fill("")]);
    rows.push(["Current Assets", ...totalCurrentAssets]);
    rows.push(["Fixed Assets", ...totalFixedAssets]);
    rows.push([
      "Total Assets",
      ...extractedData.map(
        (_, i) => totalCurrentAssets[i] + totalFixedAssets[i]
      ),
    ]);
  }

  const worksheet = XLSX.utils.aoa_to_sheet(rows);

  // Set column widths
  worksheet["!cols"] = [
    { width: 25 },
    ...extractedData.map(() => ({ width: 15 })),
  ];

  return worksheet;
};

const createPLSummary = (extractedData: BalanceSheetYear[]): XLSX.WorkSheet => {
  const headers = [
    "Item",
    ...extractedData.map((d) => `${d.year} (${d.yearType})`),
  ];
  const rows: any[][] = [headers];

  if (extractedData.length > 0) {
    const sampleData = extractedData[0];

    Object.keys(sampleData.profitLoss).forEach((key) => {
      const values = extractedData.map((d) => d.profitLoss[key] || 0);
      rows.push([formatFieldName(key), ...values]);
    });
  }

  const worksheet = XLSX.utils.aoa_to_sheet(rows);

  // Set column widths
  worksheet["!cols"] = [
    { width: 25 },
    ...extractedData.map(() => ({ width: 15 })),
  ];

  return worksheet;
};

const createRatiosSheet = (analysisResults: any): XLSX.WorkSheet => {
  const data = [
    ["Financial Ratios Analysis", ""],
    ["", ""],
    ["Ratio", "Value"],
    ["Current Ratio", analysisResults.currentRatio || "N/A"],
    ["Quick Ratio", analysisResults.quickRatio || "N/A"],
    ["Debt to Equity", analysisResults.debtToEquity || "N/A"],
    ["Return on Assets", analysisResults.returnOnAssets || "N/A"],
    ["Return on Equity", analysisResults.returnOnEquity || "N/A"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);

  // Set column widths
  worksheet["!cols"] = [{ width: 25 }, { width: 15 }];

  return worksheet;
};

const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};
