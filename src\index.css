* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color: #2c3e50;
  background-color: #f5f5f5;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  color: #2c3e50;
  font-weight: 600;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #3498db;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

button:focus,
button:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

input[type="number"] {
  border-radius: 4px;
  border: 1px solid #bdc3c7;
  padding: 0.5rem;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

input[type="number"]:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
