.pricing-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 4rem;
}

.pricing-hero {
  padding: 4rem 2rem 2rem;
  text-align: center;
  color: white;
}

.pricing-hero-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.pricing-hero-content p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.billing-toggle {
  display: inline-flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.billing-toggle button {
  background: transparent;
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.billing-toggle button.active {
  background: white;
  color: #667eea;
  font-weight: 600;
}

.discount-badge {
  background: #22c55e;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  margin-left: 0.5rem;
  font-weight: 600;
}

.lifetime-offer-banner {
  max-width: 1200px;
  margin: 0 auto 3rem;
  padding: 0 2rem;
}

.lifetime-offer-content {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 16px;
  padding: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.lifetime-offer-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.offer-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  text-align: center;
}

.savings {
  background: #dc2626;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px 8px 0 0;
  font-weight: 700;
  font-size: 1.1rem;
  display: block;
}

.time-left {
  background: #1f2937;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 0 0 8px 8px;
  font-size: 0.9rem;
  display: block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.offer-details {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.offer-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.offer-text h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.offer-pricing {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: auto;
}

.original-price {
  text-decoration: line-through;
  opacity: 0.7;
  font-size: 1rem;
}

.offer-price {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.lifetime-btn {
  background: white;
  color: #f59e0b;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.lifetime-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.pricing-plans {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.pricing-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.pricing-card.popular {
  border: 3px solid #667eea;
  transform: scale(1.05);
  box-shadow: 0 25px 50px rgba(102, 126, 234, 0.25);
  position: relative;
  z-index: 2;
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #667eea;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.plan-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.plan-header p {
  color: #718096;
  font-size: 0.95rem;
}

.plan-pricing {
  text-align: center;
  margin-bottom: 2rem;
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
}

.currency {
  font-size: 1.2rem;
  font-weight: 600;
  color: #4a5568;
}

.amount {
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
}

.duration {
  font-size: 1rem;
  color: #718096;
}

.yearly-savings {
  color: #22c55e;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: #4a5568;
}

.plan-features li svg {
  color: #22c55e;
  flex-shrink: 0;
}

.plan-limitations {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.plan-limitations h4 {
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 0.5rem;
}

.plan-limitations ul {
  list-style: none;
  padding: 0;
}

.plan-limitations li {
  color: #a0aec0;
  font-size: 0.9rem;
  padding: 0.25rem 0;
}

.plan-button {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plan-button.blue {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  color: white;
}

.plan-button.purple {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.plan-button.gold {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.plan-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.pricing-faq {
  max-width: 1000px;
  margin: 4rem auto 0;
  padding: 0 2rem;
  color: white;
}

.pricing-faq h2 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.faq-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: white;
}

.faq-item p {
  color: #e2e8f0;
  line-height: 1.6;
}

@media (max-width: 1024px) {
  .pricing-plans {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pricing-card.popular {
    transform: none;
    margin: 1rem 0;
  }
}

@media (max-width: 768px) {
  .pricing-hero-content h1 {
    font-size: 2rem;
  }

  .pricing-plans {
    grid-template-columns: 1fr;
    padding: 0 1rem;
  }

  .pricing-card.popular {
    transform: none;
  }

  .offer-details {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .offer-pricing {
    margin-left: 0;
  }
}
