import React, { useState } from "react";
import {
  Check,
  X,
  Crown,
  Zap,
  Gift,
  ArrowRight,
  Star,
  Clock,
  Users,
  BarChart3,
} from "lucide-react";
import { SUBSCRIPTION_PLANS, SubscriptionPlanType } from "../../types/Auth";
import "./PricingPage.css";

interface Props {
  onSelectPlan: (planType: SubscriptionPlanType) => void;
  onBack: () => void;
  currentPlan?: SubscriptionPlanType;
}

const PricingPage: React.FC<Props> = ({
  onSelectPlan,
  onBack,
  currentPlan,
}) => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">(
    "monthly"
  );

  const handlePlanSelect = (planType: SubscriptionPlanType) => {
    onSelectPlan(planType);
  };

  return (
    <div className="pricing-page">
      <div className="pricing-container">
        {/* Header */}
        <div className="pricing-header">
          <button onClick={onBack} className="back-button">
            ← Back
          </button>
          <div className="header-content">
            <div className="brand-logo">
              <BarChart3 className="brand-icon" />
              <span className="brand-name">Zheet.io</span>
            </div>
            <h1 className="pricing-title">Choose Your Plan</h1>
            <p className="pricing-subtitle">
              Select the perfect plan for your financial analysis needs
            </p>
          </div>
        </div>

        {/* Launch Offer Banner */}
        <div className="launch-banner">
          <div className="banner-content">
            <Gift className="banner-icon" />
            <div className="banner-text">
              <span className="banner-title">🚀 LAUNCH SPECIAL</span>
              <span className="banner-description">
                Get Lifetime Access for just $100 (90% OFF) - Limited Time!
              </span>
            </div>
            <div className="banner-timer">
              <Clock className="timer-icon" />
              <span>Offer ends soon!</span>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="pricing-grid">
          {/* Free Plan */}
          <div
            className={`pricing-card ${
              currentPlan === "free" ? "current" : ""
            }`}
          >
            <div className="card-header">
              <div className="plan-badge free">
                <Zap className="badge-icon" />
                <span>Free Trial</span>
              </div>
              <h3 className="plan-name">{SUBSCRIPTION_PLANS.free.name}</h3>
              <div className="plan-price">
                <span className="price-amount">$0</span>
                <span className="price-period">for 1 day</span>
              </div>
              <p className="plan-description">
                {SUBSCRIPTION_PLANS.free.description}
              </p>
            </div>

            <div className="card-features">
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>1 Balance Sheet Analysis</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Basic AI Extraction</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Standard Reports</span>
              </div>
              <div className="feature-item">
                <X className="feature-icon excluded" />
                <span>Excel Export</span>
              </div>
              <div className="feature-item">
                <X className="feature-icon excluded" />
                <span>Priority Support</span>
              </div>
              <div className="feature-item">
                <X className="feature-icon excluded" />
                <span>API Access</span>
              </div>
            </div>

            <button
              onClick={() => handlePlanSelect("free")}
              className={`plan-button ${
                currentPlan === "free" ? "current" : "free"
              }`}
              disabled={currentPlan === "free"}
            >
              {currentPlan === "free" ? "Current Plan" : "Start Free Trial"}
            </button>
          </div>

          {/* Premium Plan */}
          <div
            className={`pricing-card ${
              currentPlan === "premium" ? "current" : ""
            }`}
          >
            <div className="card-header">
              <div className="plan-badge premium">
                <Star className="badge-icon" />
                <span>Most Popular</span>
              </div>
              <h3 className="plan-name">{SUBSCRIPTION_PLANS.premium.name}</h3>
              <div className="plan-price">
                <span className="price-amount">
                  ${SUBSCRIPTION_PLANS.premium.price}
                </span>
                <span className="price-period">per month</span>
              </div>
              <p className="plan-description">
                {SUBSCRIPTION_PLANS.premium.description}
              </p>
            </div>

            <div className="card-features">
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>100 Analyses per month</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Advanced AI Extraction</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Excel Export</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Priority Support</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Advanced Analytics</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Custom Reports</span>
              </div>
              <div className="feature-item">
                <X className="feature-icon excluded" />
                <span>API Access</span>
              </div>
            </div>

            <button
              onClick={() => handlePlanSelect("premium")}
              className={`plan-button ${
                currentPlan === "premium" ? "current" : "premium"
              }`}
              disabled={currentPlan === "premium"}
            >
              {currentPlan === "premium" ? "Current Plan" : "Choose Premium"}
              <ArrowRight className="btn-icon" />
            </button>
          </div>

          {/* Lifetime Plan */}
          <div
            className={`pricing-card lifetime ${
              currentPlan === "lifetime" ? "current" : ""
            }`}
          >
            <div className="launch-badge">
              <Gift className="launch-icon" />
              <span>LAUNCH OFFER</span>
            </div>

            <div className="card-header">
              <div className="plan-badge lifetime">
                <Crown className="badge-icon" />
                <span>Best Value</span>
              </div>
              <h3 className="plan-name">{SUBSCRIPTION_PLANS.lifetime.name}</h3>
              <div className="plan-price">
                <span className="price-amount">
                  ${SUBSCRIPTION_PLANS.lifetime.price}
                </span>
                <span className="price-original">
                  ${SUBSCRIPTION_PLANS.lifetime.originalPrice}
                </span>
                <span className="price-period">one-time payment</span>
              </div>
              <div className="savings-badge">Save 90%</div>
              <p className="plan-description">
                {SUBSCRIPTION_PLANS.lifetime.description}
              </p>
            </div>

            <div className="card-features">
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Unlimited Analyses</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Advanced AI Extraction</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Excel Export</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Priority Support</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Advanced Analytics</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Custom Reports</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Full API Access</span>
              </div>
              <div className="feature-item">
                <Check className="feature-icon included" />
                <span>Lifetime Updates</span>
              </div>
            </div>

            <button
              onClick={() => handlePlanSelect("lifetime")}
              className={`plan-button ${
                currentPlan === "lifetime" ? "current" : "lifetime"
              }`}
              disabled={currentPlan === "lifetime"}
            >
              {currentPlan === "lifetime"
                ? "Current Plan"
                : "Get Lifetime Access"}
              <Crown className="btn-icon" />
            </button>
          </div>
        </div>

        {/* Features Comparison */}
        <div className="features-comparison">
          <h2 className="comparison-title">Feature Comparison</h2>
          <div className="comparison-table">
            <div className="table-header">
              <div className="feature-column">Features</div>
              <div className="plan-column">Free</div>
              <div className="plan-column">Premium</div>
              <div className="plan-column">Lifetime</div>
            </div>

            <div className="table-row">
              <div className="feature-name">Monthly Analyses</div>
              <div className="feature-value">1</div>
              <div className="feature-value">100</div>
              <div className="feature-value">Unlimited</div>
            </div>

            <div className="table-row">
              <div className="feature-name">AI Data Extraction</div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
            </div>

            <div className="table-row">
              <div className="feature-name">Excel Export</div>
              <div className="feature-value">
                <X className="x-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
            </div>

            <div className="table-row">
              <div className="feature-name">Priority Support</div>
              <div className="feature-value">
                <X className="x-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
            </div>

            <div className="table-row">
              <div className="feature-name">API Access</div>
              <div className="feature-value">
                <X className="x-icon" />
              </div>
              <div className="feature-value">
                <X className="x-icon" />
              </div>
              <div className="feature-value">
                <Check className="check-icon" />
              </div>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="trust-section">
          <div className="trust-stats">
            <div className="trust-item">
              <Users className="trust-icon" />
              <span className="trust-number">2,500+</span>
              <span className="trust-label">Happy Customers</span>
            </div>
            <div className="trust-item">
              <Star className="trust-icon" />
              <span className="trust-number">4.9/5</span>
              <span className="trust-label">Customer Rating</span>
            </div>
            <div className="trust-item">
              <Zap className="trust-icon" />
              <span className="trust-number">90%+</span>
              <span className="trust-label">Accuracy Rate</span>
            </div>
          </div>

          <div className="guarantee">
            <h3>30-Day Money-Back Guarantee</h3>
            <p>
              Not satisfied? Get a full refund within 30 days, no questions
              asked.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
