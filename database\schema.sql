-- Zheet.io Database Schema
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('user', 'manager', 'super_admin');
CREATE TYPE subscription_type AS ENUM ('free', 'premium', 'lifetime');
CREATE TYPE subscription_status AS ENUM ('active', 'expired', 'cancelled');
CREATE TYPE analysis_type AS ENUM ('manual', 'upload', 'generated');
CREATE TYPE analysis_status AS ENUM ('draft', 'completed', 'archived');
CREATE TYPE year_type AS ENUM ('Previous Year (Audited)', 'Current Year (Actual)', 'Projected Next Year');
CREATE TYPE extraction_status AS ENUM ('pending', 'processing', 'completed', 'error');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    role user_role DEFAULT 'user',
    subscription_type subscription_type DEFAULT 'free',
    subscription_status subscription_status DEFAULT 'active',
    subscription_start_date TIMESTAMPTZ DEFAULT NOW(),
    subscription_end_date TIMESTAMPTZ,
    usage_count INTEGER DEFAULT 0,
    max_usage INTEGER DEFAULT 1, -- Free tier gets 1 analysis
    is_email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    profile_image TEXT,
    company TEXT,
    phone TEXT
);

-- Companies table
CREATE TABLE public.companies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    company_name TEXT NOT NULL,
    address TEXT NOT NULL,
    contact_person TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT NOT NULL,
    pan_number TEXT,
    gst_number TEXT,
    number_of_years INTEGER DEFAULT 3,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Balance Sheet Analyses table
CREATE TABLE public.balance_sheet_analyses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE NOT NULL,
    analysis_name TEXT NOT NULL,
    analysis_type analysis_type NOT NULL,
    status analysis_status DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Balance Sheet Data table (stores yearly data)
CREATE TABLE public.balance_sheet_data (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    analysis_id UUID REFERENCES public.balance_sheet_analyses(id) ON DELETE CASCADE NOT NULL,
    year TEXT NOT NULL,
    year_type year_type NOT NULL,
    liabilities_data JSONB NOT NULL DEFAULT '{}',
    assets_data JSONB NOT NULL DEFAULT '{}',
    profit_loss_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(analysis_id, year, year_type)
);

-- Uploaded Files table (for PDF/image uploads)
CREATE TABLE public.uploaded_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    analysis_id UUID REFERENCES public.balance_sheet_analyses(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_type TEXT NOT NULL,
    extraction_status extraction_status DEFAULT 'pending',
    extracted_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Activity Log table (for analytics)
CREATE TABLE public.user_activity_log (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    activity_type TEXT NOT NULL,
    activity_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payment Transactions table (for Stripe integration)
CREATE TABLE public.payment_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    stripe_payment_intent_id TEXT UNIQUE,
    stripe_customer_id TEXT,
    amount INTEGER NOT NULL, -- Amount in cents
    currency TEXT DEFAULT 'inr',
    status TEXT NOT NULL,
    subscription_type subscription_type,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_subscription ON public.users(subscription_type, subscription_status);
CREATE INDEX idx_companies_user_id ON public.companies(user_id);
CREATE INDEX idx_analyses_user_id ON public.balance_sheet_analyses(user_id);
CREATE INDEX idx_analyses_company_id ON public.balance_sheet_analyses(company_id);
CREATE INDEX idx_balance_sheet_data_analysis_id ON public.balance_sheet_data(analysis_id);
CREATE INDEX idx_uploaded_files_user_id ON public.uploaded_files(user_id);
CREATE INDEX idx_uploaded_files_analysis_id ON public.uploaded_files(analysis_id);
CREATE INDEX idx_activity_log_user_id ON public.user_activity_log(user_id);
CREATE INDEX idx_activity_log_created_at ON public.user_activity_log(created_at);
CREATE INDEX idx_payment_transactions_user_id ON public.payment_transactions(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analyses_updated_at BEFORE UPDATE ON public.balance_sheet_analyses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_balance_sheet_data_updated_at BEFORE UPDATE ON public.balance_sheet_data FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON public.payment_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.balance_sheet_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.balance_sheet_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.uploaded_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- Companies policies
CREATE POLICY "Users can view own companies" ON public.companies FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own companies" ON public.companies FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own companies" ON public.companies FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own companies" ON public.companies FOR DELETE USING (auth.uid() = user_id);

-- Balance sheet analyses policies
CREATE POLICY "Users can view own analyses" ON public.balance_sheet_analyses FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own analyses" ON public.balance_sheet_analyses FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own analyses" ON public.balance_sheet_analyses FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own analyses" ON public.balance_sheet_analyses FOR DELETE USING (auth.uid() = user_id);

-- Balance sheet data policies
CREATE POLICY "Users can view own balance sheet data" ON public.balance_sheet_data FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.balance_sheet_analyses WHERE id = analysis_id AND user_id = auth.uid())
);
CREATE POLICY "Users can insert own balance sheet data" ON public.balance_sheet_data FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM public.balance_sheet_analyses WHERE id = analysis_id AND user_id = auth.uid())
);
CREATE POLICY "Users can update own balance sheet data" ON public.balance_sheet_data FOR UPDATE USING (
    EXISTS (SELECT 1 FROM public.balance_sheet_analyses WHERE id = analysis_id AND user_id = auth.uid())
);
CREATE POLICY "Users can delete own balance sheet data" ON public.balance_sheet_data FOR DELETE USING (
    EXISTS (SELECT 1 FROM public.balance_sheet_analyses WHERE id = analysis_id AND user_id = auth.uid())
);

-- Uploaded files policies
CREATE POLICY "Users can view own uploaded files" ON public.uploaded_files FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own uploaded files" ON public.uploaded_files FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own uploaded files" ON public.uploaded_files FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own uploaded files" ON public.uploaded_files FOR DELETE USING (auth.uid() = user_id);

-- Activity log policies (users can only view their own activity)
CREATE POLICY "Users can view own activity" ON public.user_activity_log FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert activity" ON public.user_activity_log FOR INSERT WITH CHECK (true);

-- Payment transactions policies
CREATE POLICY "Users can view own transactions" ON public.payment_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert transactions" ON public.payment_transactions FOR INSERT WITH CHECK (true);
CREATE POLICY "System can update transactions" ON public.payment_transactions FOR UPDATE USING (true);

-- Admin policies (super_admin can see everything)
CREATE POLICY "Super admin can view all users" ON public.users FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'super_admin')
);
CREATE POLICY "Super admin can view all companies" ON public.companies FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'super_admin')
);
CREATE POLICY "Super admin can view all analyses" ON public.balance_sheet_analyses FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'super_admin')
);

-- Create storage bucket for uploaded files
INSERT INTO storage.buckets (id, name, public) VALUES ('balance-sheet-uploads', 'balance-sheet-uploads', false);

-- Storage policies
CREATE POLICY "Users can upload their own files" ON storage.objects FOR INSERT WITH CHECK (
    bucket_id = 'balance-sheet-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
);
CREATE POLICY "Users can view their own files" ON storage.objects FOR SELECT USING (
    bucket_id = 'balance-sheet-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
);
CREATE POLICY "Users can delete their own files" ON storage.objects FOR DELETE USING (
    bucket_id = 'balance-sheet-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
);
