.analysis-choice {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.choice-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 3rem;
  max-width: 1000px;
  width: 100%;
}

.choice-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.choice-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.choice-header h2 {
  color: #34495e;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.choice-header p {
  color: #7f8c8d;
  font-size: 1.2rem;
  line-height: 1.6;
}

.choice-options {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.analysis-options {
  grid-template-columns: 1fr 1fr;
  max-width: 900px;
  margin: 0 auto 3rem auto;
}

.back-to-home {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(52, 152, 219, 0.3);
  color: #3498db;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.back-to-home:hover {
  color: white !important;
  background: #3498db;
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.back-icon {
  width: 16px;
  height: 16px;
}

.choice-card {
  background: white;
  border: 3px solid #e1e8ed;
  border-radius: 16px;
  padding: 2.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.choice-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.analyze-card::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.upload-card::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.create-card::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.choice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.analyze-card:hover {
  border-color: #3498db;
  box-shadow: 0 20px 40px rgba(52, 152, 219, 0.2);
}

.upload-card:hover {
  border-color: #9b59b6;
  box-shadow: 0 20px 40px rgba(155, 89, 182, 0.2);
}

.create-card:hover {
  border-color: #27ae60;
  box-shadow: 0 20px 40px rgba(39, 174, 96, 0.2);
}

.choice-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
}

.choice-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
}

.choice-icon-wrapper.manual {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.choice-icon-wrapper.upload {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.choice-icon-wrapper .choice-icon {
  width: 40px;
  height: 40px;
  color: white;
  font-size: inherit;
  margin: 0;
}

.choice-card h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.choice-card p {
  color: #7f8c8d;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.choice-card ul {
  text-align: left;
  color: #34495e;
  margin-bottom: 2rem;
  padding-left: 1.5rem;
}

.choice-card li {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.choice-button {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: auto;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button-icon {
  width: 20px;
  height: 20px;
}

.analyze-card:hover .choice-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.upload-card:hover .choice-button {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
}

.create-card:hover .choice-button {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.choice-button span {
  font-weight: 600;
  font-size: 1.1rem;
}

.choice-footer {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #bdc3c7;
}

.choice-footer p {
  color: #7f8c8d;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.choice-footer strong {
  color: #2c3e50;
}

@media (max-width: 768px) {
  .analysis-choice {
    padding: 1rem;
  }

  .choice-container {
    padding: 2rem;
  }

  .choice-options {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .choice-header h1 {
    font-size: 2rem;
  }

  .choice-header h2 {
    font-size: 1.5rem;
  }

  .choice-card {
    padding: 2rem;
  }

  .choice-icon {
    font-size: 3rem;
  }
}
