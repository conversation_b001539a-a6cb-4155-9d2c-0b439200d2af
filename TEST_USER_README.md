# Test User Account - FinanceAI Pro

## 🧪 Test Account Credentials

**Username:** `vivek.s.rajiv`  
**Password:** `Tester@Wholesite`

## 🎯 Purpose

This test account provides **unlimited access** to all features of the FinanceAI Pro platform without any payment or subscription restrictions. It's designed for:

- **Testing all features** without limitations
- **Demonstrating the platform** to potential users
- **Quality assurance** and bug testing
- **Development and debugging** purposes

## ✨ Features & Access

### 🔓 **Unlimited Access**
- **Unlimited balance sheet analyses**
- **Unlimited company profiles**
- **All premium features enabled**
- **No time restrictions**
- **No usage limits**

### 📊 **Available Features**
- ✅ Balance Sheet Analysis (unlimited)
- ✅ Multi-year data entry (2-5 years)
- ✅ AI-powered PDF/image extraction
- ✅ Excel export functionality
- ✅ Professional PDF reports
- ✅ Advanced analytics and ratios
- ✅ Working capital assessment
- ✅ P&L analysis
- ✅ Custom reports
- ✅ Priority support access
- ✅ API access (when available)

### 👤 **User Profile**
- **Name:** Vivek Rajiv
- **Role:** Super Admin
- **Subscription:** Lifetime Access
- **Company:** FinanceAI Pro Testing
- **Phone:** **********
- **Email Verified:** Yes

## 🚀 **How to Use**

### **Method 1: Manual Login**
1. Go to the login page
2. Enter username: `vivek.s.rajiv`
3. Enter password: `Tester@Wholesite`
4. Click "Sign In"

### **Method 2: Auto-Fill (Recommended)**
1. Go to the login page
2. Look for the "🧪 Test Account" section
3. Click "🚀 Auto-Fill Test Credentials"
4. Click "Sign In"

## 🔧 **Technical Details**

### **User Configuration**
```typescript
{
  id: "test-user-vivek",
  email: "vivek.s.rajiv",
  firstName: "Vivek",
  lastName: "Rajiv",
  role: "super_admin",
  subscription: {
    type: "lifetime",
    status: "active",
    features: {
      maxAnalyses: -1,        // Unlimited
      maxCompanies: -1,       // Unlimited
      exportToExcel: true,
      prioritySupport: true,
      advancedAnalytics: true,
      apiAccess: true,
      customReports: true
    }
  }
}
```

### **Bypass Mechanisms**
- **Payment restrictions:** Completely bypassed
- **Usage limits:** Set to unlimited (-1)
- **Feature restrictions:** All features enabled
- **Time limits:** No expiration date
- **Subscription checks:** Always returns active lifetime

## 🛡️ **Security Notes**

- This is a **test account only** - not for production use
- Credentials are visible in the login page for easy access
- Account has super admin privileges
- All activities are logged for debugging purposes

## 🔍 **Debugging & Monitoring**

The test user activities are logged to the browser console with the prefix `🧪 Test User Activity:` for easy debugging and monitoring.

## 📞 **Contact Information**

**Test User Contact Details:**
- **Name:** Vivek Rajiv S
- **Mobile:** **********
- **Email:** <EMAIL>

---

**Note:** This test account is specifically created for comprehensive testing of the FinanceAI Pro platform and provides unrestricted access to all features and functionalities.
