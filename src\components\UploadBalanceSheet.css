.upload-balance-sheet {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.upload-container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.upload-header {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
  z-index: 10;
}

.upload-header h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  z-index: 20;
  position: relative;
}

.upload-header h2 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  z-index: 20;
  position: relative;
}

.upload-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
  z-index: 20;
  position: relative;
}

.back-button {
  position: absolute;
  left: 2rem;
  top: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 30;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(155, 89, 182, 0.3);
}

.upload-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.upload-header h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.upload-header p {
  font-size: 1.1rem;
  opacity: 0.8;
  line-height: 1.6;
}

.upload-zone {
  margin: 3rem;
  border: 3px dashed #9b59b6;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.upload-zone:hover,
.upload-zone.drag-active {
  border-color: #8e44ad;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(155, 89, 182, 0.2);
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.upload-zone h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.upload-zone p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  pointer-events: all;
}

.upload-info {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.upload-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #34495e;
}

.uploaded-files {
  margin: 0 3rem 3rem 3rem;
}

.uploaded-files h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
}

.file-item.processing {
  border-color: #f39c12;
  background: linear-gradient(135deg, #fff, #fef9e7);
}

.file-item.completed {
  border-color: #27ae60;
  background: linear-gradient(135deg, #fff, #eafaf1);
}

.file-item.error {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #fff, #fdedec);
}

.file-preview {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
}

.file-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pdf-icon {
  font-size: 2rem;
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.file-size {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.file-settings {
  display: flex;
  gap: 1rem;
}

.file-settings select {
  padding: 0.5rem;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  background: white;
  color: #2c3e50;
  font-size: 0.9rem;
}

.file-status {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.status-pending {
  color: #f39c12;
}
.status-processing {
  color: #3498db;
}
.status-completed {
  color: #27ae60;
}
.status-error {
  color: #e74c3c;
}

.remove-file {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.remove-file:hover:not(:disabled) {
  background: #f8f9fa;
  transform: scale(1.1);
}

.remove-file:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.upload-actions {
  text-align: center;
  margin: 0 3rem 3rem 3rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.process-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(155, 89, 182, 0.3);
}

.process-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.export-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
  justify-content: center;
}

.export-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
}

.export-btn .btn-icon {
  transition: transform 0.3s ease;
}

.export-btn:hover .btn-icon {
  transform: translateX(3px);
}

.upload-features {
  background: #f8f9fa;
  padding: 3rem;
  text-align: center;
}

.upload-features h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2rem;
}

.feature-text {
  font-weight: 600;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .upload-balance-sheet {
    padding: 1rem;
  }

  .upload-container {
    margin: 0;
  }

  .upload-header {
    padding: 1.5rem;
  }

  .back-button {
    position: static;
    margin-bottom: 1rem;
  }

  .upload-header h1 {
    font-size: 2rem;
  }

  .upload-zone {
    margin: 2rem;
    padding: 2rem;
  }

  .uploaded-files {
    margin: 0 2rem 2rem 2rem;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .file-settings {
    width: 100%;
  }

  .file-settings select {
    flex: 1;
  }

  .upload-actions {
    margin: 0 2rem 2rem 2rem;
  }

  .upload-features {
    padding: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
