import React, { useState, useCallback } from "react";
import { Download } from "lucide-react";
import { CompanyInfo, BalanceSheetYear } from "../types/FinancialData";
import { exportToExcel } from "../utils/excelExport";
import "./UploadBalanceSheet.css";

interface Props {
  company: CompanyInfo;
  onDataExtracted: (yearData: BalanceSheetYear[]) => void;
  onBack: () => void;
}

interface UploadedFile {
  file: File;
  id: string;
  year: string;
  yearType:
    | "Previous Year (Audited)"
    | "Current Year (Actual)"
    | "Projected Next Year";
  status: "pending" | "processing" | "completed" | "error";
  extractedData?: BalanceSheetYear;
  preview?: string;
}

const UploadBalanceSheet: React.FC<Props> = ({
  company,
  onDataExtracted,
  onBack,
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [extractedData, setExtractedData] = useState<BalanceSheetYear[]>([]);

  // Generate year types and years based on company's numberOfYears setting
  const generateYearTypes = (numberOfYears: number) => {
    const types: Array<
      | "Previous Year (Audited)"
      | "Current Year (Actual)"
      | "Projected Next Year"
    > = [];

    if (numberOfYears >= 2) {
      types.push("Previous Year (Audited)");
    }
    types.push("Current Year (Actual)");
    if (numberOfYears >= 3) {
      types.push("Projected Next Year");
    }

    // Add additional years if more than 3
    for (let i = 4; i <= numberOfYears; i++) {
      types.push("Projected Next Year" as const);
    }

    return types;
  };

  const generateYears = (numberOfYears: number) => {
    const currentYear = new Date().getFullYear();
    const years: string[] = [];

    // Start from previous years if numberOfYears >= 2
    const startYear = numberOfYears >= 2 ? currentYear - 1 : currentYear;

    for (let i = 0; i < numberOfYears; i++) {
      years.push((startYear + i).toString());
    }

    return years;
  };

  const yearTypes = generateYearTypes(company.numberOfYears);
  const years = generateYears(company.numberOfYears);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (files: File[]) => {
    const validFiles = files.filter((file) => {
      const isValidType =
        file.type === "application/pdf" || file.type.startsWith("image/");
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit
      return isValidType && isValidSize;
    });

    const newFiles: UploadedFile[] = validFiles.map((file, index) => ({
      file,
      id: Date.now() + index + "",
      year: years[Math.min(index, years.length - 1)],
      yearType: yearTypes[Math.min(index, yearTypes.length - 1)],
      status: "pending",
    }));

    setUploadedFiles((prev) => [...prev, ...newFiles]);

    // Create preview for images
    newFiles.forEach((uploadedFile) => {
      if (uploadedFile.file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setUploadedFiles((prev) =>
            prev.map((f) =>
              f.id === uploadedFile.id
                ? { ...f, preview: e.target?.result as string }
                : f
            )
          );
        };
        reader.readAsDataURL(uploadedFile.file);
      }
    });
  };

  const updateFileDetails = (
    id: string,
    year: string,
    yearType: (typeof yearTypes)[number]
  ) => {
    setUploadedFiles((prev) =>
      prev.map((f) => (f.id === id ? { ...f, year, yearType } : f))
    );
  };

  const removeFile = (id: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== id));
  };

  const extractDataFromFile = async (
    uploadedFile: UploadedFile
  ): Promise<BalanceSheetYear> => {
    // Simulate AI/OCR data extraction
    // In a real implementation, this would call an OCR service like Tesseract.js or cloud OCR API

    return new Promise((resolve) => {
      setTimeout(() => {
        // Mock extracted data - in reality this would come from OCR/AI processing
        const mockData: BalanceSheetYear = {
          year: uploadedFile.year,
          yearType: uploadedFile.yearType,
          liabilities: {
            currentLiabilities: {
              tradeCreditors: Math.floor(Math.random() * 500000) + 100000,
              expensesDutyOtherPayables:
                Math.floor(Math.random() * 200000) + 50000,
              lcCreditorsJobWork: Math.floor(Math.random() * 150000) + 30000,
              advanceReceivedFromBuyers:
                Math.floor(Math.random() * 100000) + 20000,
              shortTermBankFinance: Math.floor(Math.random() * 300000) + 100000,
              provisions: Math.floor(Math.random() * 80000) + 20000,
              taxPayable: Math.floor(Math.random() * 120000) + 30000,
              commercialPaper: Math.floor(Math.random() * 50000) + 10000,
              sdMaturityRepayableOneYear:
                Math.floor(Math.random() * 40000) + 10000,
            },
            branchSisterConcerns: {
              havingCreditFacilities:
                Math.floor(Math.random() * 100000) + 20000,
              notHavingFacilities: Math.floor(Math.random() * 50000) + 10000,
            },
            longTermLiabilities: {
              longTermFundsFromFamily:
                Math.floor(Math.random() * 200000) + 50000,
              longTermLoanFromBank: Math.floor(Math.random() * 500000) + 200000,
              longTermLoanFromInstitutions:
                Math.floor(Math.random() * 300000) + 100000,
              cautionDeposit: Math.floor(Math.random() * 50000) + 10000,
            },
            capitalAndReserve: {
              paidUpCapital: Math.floor(Math.random() * 1000000) + 500000,
              reservesAndSurplus: Math.floor(Math.random() * 800000) + 300000,
              convertiblePreferenceShare:
                Math.floor(Math.random() * 200000) + 50000,
              plAccount: Math.floor(Math.random() * 300000) + 100000,
              lessMiscExpensesDrawings:
                Math.floor(Math.random() * 100000) + 20000,
            },
          },
          assets: {
            currentAssets: {
              cashAndBankBalance: Math.floor(Math.random() * 400000) + 100000,
              stock: Math.floor(Math.random() * 600000) + 200000,
              receivables: Math.floor(Math.random() * 500000) + 150000,
              shortTermAdvance: Math.floor(Math.random() * 100000) + 30000,
              otherCurrentAsset: Math.floor(Math.random() * 80000) + 20000,
              depositAdvances: Math.floor(Math.random() * 120000) + 40000,
              others: Math.floor(Math.random() * 60000) + 20000,
            },
            fixedAssets: {
              landAndBuilding: Math.floor(Math.random() * 1500000) + 500000,
              plantMachinery: Math.floor(Math.random() * 2000000) + 800000,
              fittingsFurniture: Math.floor(Math.random() * 200000) + 50000,
              vehicles: Math.floor(Math.random() * 300000) + 100000,
              officeEquipments: Math.floor(Math.random() * 150000) + 50000,
              advanceAgainstCapitalGoods:
                Math.floor(Math.random() * 100000) + 30000,
            },
            nonCurrentAssets: {
              investment: Math.floor(Math.random() * 500000) + 100000,
              deferredTaxOthers: Math.floor(Math.random() * 100000) + 30000,
            },
            others: {
              otherAdvance: Math.floor(Math.random() * 80000) + 20000,
              buildingDepositOtherDeposit:
                Math.floor(Math.random() * 120000) + 40000,
              inventoryProject: Math.floor(Math.random() * 100000) + 30000,
            },
          },
          profitLoss: {
            grossSalesDomestic: Math.floor(Math.random() * 5000000) + 2000000,
            exportSales: Math.floor(Math.random() * 2000000) + 500000,
            commissionSales: Math.floor(Math.random() * 300000) + 100000,
            interest: Math.floor(Math.random() * 200000) + 50000,
            tax: Math.floor(Math.random() * 300000) + 100000,
            depreciation: Math.floor(Math.random() * 400000) + 150000,
            netProfit: Math.floor(Math.random() * 800000) + 300000,
          },
        };

        resolve(mockData);
      }, 2000 + Math.random() * 3000); // Simulate processing time
    });
  };

  const processAllFiles = async () => {
    if (uploadedFiles.length === 0) {
      alert("Please upload at least one file");
      return;
    }

    setIsProcessing(true);
    const extractedData: BalanceSheetYear[] = [];

    for (const uploadedFile of uploadedFiles) {
      try {
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === uploadedFile.id ? { ...f, status: "processing" } : f
          )
        );

        const data = await extractDataFromFile(uploadedFile);

        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === uploadedFile.id
              ? { ...f, status: "completed", extractedData: data }
              : f
          )
        );

        extractedData.push(data);
      } catch (error) {
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === uploadedFile.id ? { ...f, status: "error" } : f
          )
        );
      }
    }

    setIsProcessing(false);

    if (extractedData.length > 0) {
      setExtractedData(extractedData);
      onDataExtracted(extractedData);
    }
  };

  const handleExportToExcel = () => {
    if (extractedData.length === 0) {
      alert("No data to export. Please process files first.");
      return;
    }

    try {
      exportToExcel({
        companyInfo: company,
        extractedData: extractedData,
      });
    } catch (error) {
      console.error("Export error:", error);
      alert("Failed to export data. Please try again.");
    }
  };

  return (
    <div className="upload-balance-sheet">
      <div className="upload-container">
        <div className="upload-header">
          <button onClick={onBack} className="back-button">
            🏠 Back to Home
          </button>
          <h1>Upload Balance Sheet Analysis</h1>
          <h2>{company.companyName}</h2>
          <p>
            Upload your balance sheet documents and let AI extract the financial
            data automatically
          </p>
        </div>

        <div
          className={`upload-zone ${dragActive ? "drag-active" : ""}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="upload-content">
            <div className="upload-icon">📤</div>
            <h3>Drag & Drop Files Here</h3>
            <p>or click to browse files</p>
            <input
              type="file"
              multiple
              accept=".pdf,image/*"
              onChange={handleFileInput}
              className="file-input"
            />
            <div className="upload-info">
              <p>Supported formats: PDF, JPG, PNG, JPEG</p>
              <p>Maximum file size: 10MB per file</p>
            </div>
          </div>
        </div>

        {uploadedFiles.length > 0 && (
          <div className="uploaded-files">
            <h3>Uploaded Files ({uploadedFiles.length})</h3>
            <div className="files-list">
              {uploadedFiles.map((uploadedFile) => (
                <div
                  key={uploadedFile.id}
                  className={`file-item ${uploadedFile.status}`}
                >
                  <div className="file-preview">
                    {uploadedFile.preview ? (
                      <img src={uploadedFile.preview} alt="Preview" />
                    ) : (
                      <div className="pdf-icon">📄</div>
                    )}
                  </div>

                  <div className="file-details">
                    <div className="file-name">{uploadedFile.file.name}</div>
                    <div className="file-size">
                      {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
                    </div>

                    <div className="file-settings">
                      <select
                        value={uploadedFile.year}
                        onChange={(e) =>
                          updateFileDetails(
                            uploadedFile.id,
                            e.target.value,
                            uploadedFile.yearType
                          )
                        }
                        disabled={uploadedFile.status === "processing"}
                      >
                        {years.map((year) => (
                          <option key={year} value={year}>
                            {year}
                          </option>
                        ))}
                      </select>

                      <select
                        value={uploadedFile.yearType}
                        onChange={(e) =>
                          updateFileDetails(
                            uploadedFile.id,
                            uploadedFile.year,
                            e.target.value as any
                          )
                        }
                        disabled={uploadedFile.status === "processing"}
                      >
                        {yearTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="file-status">
                    {uploadedFile.status === "pending" && (
                      <span className="status-pending">⏳ Pending</span>
                    )}
                    {uploadedFile.status === "processing" && (
                      <span className="status-processing">
                        🔄 Processing...
                      </span>
                    )}
                    {uploadedFile.status === "completed" && (
                      <span className="status-completed">✅ Completed</span>
                    )}
                    {uploadedFile.status === "error" && (
                      <span className="status-error">❌ Error</span>
                    )}
                  </div>

                  <button
                    onClick={() => removeFile(uploadedFile.id)}
                    className="remove-file"
                    disabled={uploadedFile.status === "processing"}
                  >
                    🗑️
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="upload-actions">
          <button
            onClick={processAllFiles}
            disabled={isProcessing || uploadedFiles.length === 0}
            className="process-btn"
          >
            {isProcessing
              ? "🔄 Processing Files..."
              : "🚀 Extract Data & Analyze"}
          </button>

          {extractedData.length > 0 && (
            <button onClick={handleExportToExcel} className="export-btn">
              <Download className="btn-icon" />
              Export to Excel
            </button>
          )}
        </div>

        <div className="upload-features">
          <h3>🤖 AI-Powered Features</h3>
          <div className="features-grid">
            <div className="feature-item">
              <span className="feature-icon">🔍</span>
              <span className="feature-text">OCR Text Recognition</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🧠</span>
              <span className="feature-text">Smart Data Extraction</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">📊</span>
              <span className="feature-text">Automatic Form Population</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">⚡</span>
              <span className="feature-text">Multi-Year Processing</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadBalanceSheet;
