import { useState, useEffect, useCallback } from 'react'
import { User, LoginCredentials, RegisterData } from '../types/Auth'
import { AuthService, ActivityService } from '../services/api'
import { supabase } from '../lib/supabase'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null
  })

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const user = await AuthService.getCurrentUser()
        setAuthState({
          user,
          isAuthenticated: !!user,
          isLoading: false,
          error: null
        })
      } catch (error) {
        console.error('Auth initialization error:', error)
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: 'Failed to initialize authentication'
        })
      }
    }

    initializeAuth()

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        if (event === 'SIGNED_IN' && session?.user) {
          const user = await AuthService.getCurrentUser()
          setAuthState({
            user,
            isAuthenticated: !!user,
            isLoading: false,
            error: null
          })
          
          // Log activity
          await ActivityService.logActivity('user_login', {
            login_method: 'email',
            timestamp: new Date().toISOString()
          })
        } else if (event === 'SIGNED_OUT') {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          
          // Log activity
          await ActivityService.logActivity('user_logout', {
            timestamp: new Date().toISOString()
          })
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const signUp = useCallback(async (data: RegisterData) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const { user, error } = await AuthService.signUp(data)
      
      if (error) {
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return { success: false, error }
      }

      // Note: User will be signed in automatically after email verification
      setAuthState({
        user,
        isAuthenticated: !!user,
        isLoading: false,
        error: null
      })

      // Log activity
      await ActivityService.logActivity('user_registration', {
        registration_method: 'email',
        company: data.company,
        timestamp: new Date().toISOString()
      })

      return { success: true, error: null }
    } catch (error) {
      const errorMessage = 'Registration failed. Please try again.'
      setAuthState(prev => ({ ...prev, isLoading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const signIn = useCallback(async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const { user, error } = await AuthService.signIn(credentials)
      
      if (error) {
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return { success: false, error }
      }

      setAuthState({
        user,
        isAuthenticated: !!user,
        isLoading: false,
        error: null
      })

      return { success: true, error: null }
    } catch (error) {
      const errorMessage = 'Login failed. Please check your credentials.'
      setAuthState(prev => ({ ...prev, isLoading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const signOut = useCallback(async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }))
    
    try {
      const { error } = await AuthService.signOut()
      
      if (error) {
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return { success: false, error }
      }

      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      })

      return { success: true, error: null }
    } catch (error) {
      const errorMessage = 'Logout failed. Please try again.'
      setAuthState(prev => ({ ...prev, isLoading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const refreshUser = useCallback(async () => {
    try {
      const user = await AuthService.getCurrentUser()
      setAuthState(prev => ({
        ...prev,
        user,
        isAuthenticated: !!user
      }))
      return user
    } catch (error) {
      console.error('Refresh user error:', error)
      return null
    }
  }, [])

  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }))
  }, [])

  // Test user login (for development)
  const signInAsTestUser = useCallback(async () => {
    return signIn({
      email: '<EMAIL>',
      password: 'Tester@Wholesite'
    })
  }, [signIn])

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,
    signUp,
    signIn,
    signOut,
    refreshUser,
    clearError,
    signInAsTestUser
  }
}

// Helper hook for checking subscription features
export const useSubscription = () => {
  const { user } = useAuth()
  
  const hasFeature = useCallback((feature: keyof typeof user.subscription.features) => {
    return user?.subscription.features[feature] || false
  }, [user])

  const canCreateAnalysis = useCallback(() => {
    if (!user) return false
    return user.subscription.usageCount < user.subscription.maxUsage
  }, [user])

  const getRemainingAnalyses = useCallback(() => {
    if (!user) return 0
    return Math.max(0, user.subscription.maxUsage - user.subscription.usageCount)
  }, [user])

  const isSubscriptionActive = useCallback(() => {
    if (!user) return false
    return user.subscription.status === 'active'
  }, [user])

  return {
    subscription: user?.subscription,
    hasFeature,
    canCreateAnalysis,
    getRemainingAnalyses,
    isSubscriptionActive
  }
}
