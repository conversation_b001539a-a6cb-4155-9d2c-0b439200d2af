# Zheet.io Backend Setup Guide

## 🚀 Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a free account
2. Click "New Project"
3. Choose your organization
4. Set project name: `zheet-io-production`
5. Set database password (save this securely)
6. Choose region closest to your users (e.g., Asia Pacific for India)
7. Click "Create new project"

## 🗄️ Step 2: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the entire content from `database/schema.sql`
3. Paste it in the SQL Editor and click **Run**
4. This will create all tables, policies, and storage buckets

## 🔑 Step 3: Get API Keys

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://oartmnkcahkwjbzehdxo.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.6_beYdY8mhhurM2tEzE580qiIawp-EaeWqoP8rQufC0`)

## 🌍 Step 4: Configure Environment Variables

1. Create a `.env` file in your project root:

```bash
cp .env.example .env
```

2. Update the `.env` file with your Supabase credentials:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

## 📧 Step 5: Configure Email Authentication

1. In Supabase dashboard, go to **Authentication** → **Settings**
2. Under **SMTP Settings**, configure your email provider:

### Option A: Use Supabase's Built-in Email (Recommended for testing)

- No additional setup required
- Limited to 3 emails per hour on free tier

### Option B: Configure Custom SMTP (Recommended for production)

- **SMTP Host**: `smtp.sendgrid.net` (if using SendGrid)
- **SMTP Port**: `587`
- **SMTP Username**: `apikey`
- **SMTP Password**: *********************************************************************
- **Sender Email**: `<EMAIL>`
- **Sender Name**: `zheet.io`

3. Update email templates:
   - Go to **Authentication** → **Email Templates**
   - Customize the confirmation and reset password emails with your branding

## 🔐 Step 6: Create Test User Account

Run this SQL in your Supabase SQL Editor to create the test user:

```sql
-- Insert test user (this will be used for development/testing)
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role
) VALUES (
  gen_random_uuid(),
  '********-0000-0000-0000-********0000',
  '<EMAIL>',
  crypt('Tester@Wholesite', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"first_name": "Vivek", "last_name": "Rajiv", "company": "Zheet.io Testing"}',
  false,
  'authenticated'
);

-- Insert corresponding user profile
INSERT INTO public.users (
  id,
  email,
  first_name,
  last_name,
  role,
  subscription_type,
  subscription_status,
  max_usage,
  is_email_verified,
  company,
  phone
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  '<EMAIL>',
  'Vivek',
  'Rajiv',
  'super_admin',
  'lifetime',
  'active',
  999999,
  true,
  'Zheet.io Testing',
  '**********'
);
```

## 🔒 Step 7: Configure Row Level Security

The schema already includes RLS policies, but verify they're working:

1. Go to **Authentication** → **Policies**
2. Ensure all tables have policies enabled
3. Test with different user roles to ensure data isolation

## 📁 Step 8: Configure File Storage

1. Go to **Storage** → **Buckets**
2. Verify `balance-sheet-uploads` bucket was created
3. Check bucket policies are in place for user file access

## 🧪 Step 9: Test the Integration

1. Start your development server:

```bash
npm run dev
```

2. Test the following flows:
   - User registration
   - Email verification
   - User login
   - Company creation
   - Balance sheet data saving
   - File upload

## 📊 Step 10: Monitor and Analytics

1. Go to **Reports** in Supabase dashboard
2. Monitor:
   - Database performance
   - API usage
   - Storage usage
   - Authentication events

## 🚀 Step 11: Production Deployment

### Database Optimization:

1. **Indexes**: Already created in schema
2. **Connection Pooling**: Enable in Supabase settings
3. **Backup**: Configure automated backups

### Security:

1. **API Rate Limiting**: Configure in Supabase
2. **CORS**: Set allowed origins
3. **JWT Secret**: Rotate regularly

### Monitoring:

1. Set up alerts for:
   - High error rates
   - Database performance issues
   - Storage limits
   - API rate limits

## 🔧 Troubleshooting

### Common Issues:

1. **RLS Policies**: If users can't access data, check RLS policies
2. **Email Delivery**: Check SMTP configuration and spam folders
3. **File Upload**: Verify storage bucket policies
4. **API Errors**: Check Supabase logs in dashboard

### Debug Commands:

```sql
-- Check user creation
SELECT * FROM auth.users WHERE email = '<EMAIL>';

-- Check user profile
SELECT * FROM public.users WHERE email = '<EMAIL>';

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'users';
```

## 📞 Support

- **Supabase Docs**: [docs.supabase.com](https://docs.supabase.com)
- **Community**: [github.com/supabase/supabase/discussions](https://github.com/supabase/supabase/discussions)
- **Status**: [status.supabase.com](https://status.supabase.com)

---

## Next Steps After Backend Setup:

1. ✅ **Backend Integration** (Current)
2. 🔄 **Payment Processing** (Stripe integration)
3. 🔄 **Admin Panel** (Management interface)
4. 🔄 **Email Services** (Advanced notifications)
5. 🔄 **Analytics** (Usage tracking)

Once you complete this backend setup, we'll move on to integrating Stripe for payment processing!
