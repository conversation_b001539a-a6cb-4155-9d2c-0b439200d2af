import { BalanceSheetYear, CompanyInfo } from '../types/FinancialData';

export interface OptimalRatios {
  // Asset Distribution (as percentage of total assets)
  currentAssets: number;
  fixedAssets: number;
  nonCurrentAssets: number;
  others: number;
  
  // Current Assets Breakdown
  cashAndBank: number;
  stock: number;
  receivables: number;
  otherCurrentAssets: number;
  
  // Fixed Assets Breakdown
  landBuilding: number;
  plantMachinery: number;
  otherFixedAssets: number;
  
  // Liability Distribution (as percentage of total liabilities)
  currentLiabilities: number;
  longTermLiabilities: number;
  capitalReserves: number;
  
  // Current Liabilities Breakdown
  tradeCreditors: number;
  bankFinance: number;
  otherCurrentLiabilities: number;
  
  // Capital Structure
  paidUpCapital: number;
  reservesSurplus: number;
  retainedEarnings: number;
}

// Optimal ratios for a good score balance sheet
const OPTIMAL_RATIOS: OptimalRatios = {
  // Assets Distribution (should total 100%)
  currentAssets: 0.45,      // 45% - Good liquidity
  fixedAssets: 0.40,        // 40% - Productive assets
  nonCurrentAssets: 0.10,   // 10% - Investments
  others: 0.05,             // 5% - Other assets
  
  // Current Assets Breakdown (of current assets)
  cashAndBank: 0.25,        // 25% of current assets - Good liquidity
  stock: 0.35,              // 35% of current assets - Reasonable inventory
  receivables: 0.30,        // 30% of current assets - Good collection
  otherCurrentAssets: 0.10, // 10% of current assets - Others
  
  // Fixed Assets Breakdown (of fixed assets)
  landBuilding: 0.40,       // 40% of fixed assets
  plantMachinery: 0.50,     // 50% of fixed assets - Productive
  otherFixedAssets: 0.10,   // 10% of fixed assets
  
  // Liabilities Distribution (should total 100%)
  currentLiabilities: 0.30, // 30% - Manageable short-term debt
  longTermLiabilities: 0.20, // 20% - Reasonable long-term debt
  capitalReserves: 0.50,    // 50% - Strong equity base
  
  // Current Liabilities Breakdown (of current liabilities)
  tradeCreditors: 0.60,     // 60% of current liabilities
  bankFinance: 0.25,        // 25% of current liabilities
  otherCurrentLiabilities: 0.15, // 15% of current liabilities
  
  // Capital Structure (of capital & reserves)
  paidUpCapital: 0.40,      // 40% of capital
  reservesSurplus: 0.35,    // 35% of capital
  retainedEarnings: 0.25,   // 25% of capital
};

export function generateOptimalBalanceSheet(
  totalAssets: number,
  totalLiabilities: number,
  company: CompanyInfo,
  year: string = new Date().getFullYear().toString()
): BalanceSheetYear {
  
  // Ensure balance sheet is balanced
  const balancedTotal = Math.max(totalAssets, totalLiabilities);
  
  // Calculate asset distributions
  const currentAssetsTotal = Math.round(balancedTotal * OPTIMAL_RATIOS.currentAssets);
  const fixedAssetsTotal = Math.round(balancedTotal * OPTIMAL_RATIOS.fixedAssets);
  const nonCurrentAssetsTotal = Math.round(balancedTotal * OPTIMAL_RATIOS.nonCurrentAssets);
  const othersAssetsTotal = balancedTotal - currentAssetsTotal - fixedAssetsTotal - nonCurrentAssetsTotal;
  
  // Calculate liability distributions
  const currentLiabilitiesTotal = Math.round(balancedTotal * OPTIMAL_RATIOS.currentLiabilities);
  const longTermLiabilitiesTotal = Math.round(balancedTotal * OPTIMAL_RATIOS.longTermLiabilities);
  const capitalReservesTotal = balancedTotal - currentLiabilitiesTotal - longTermLiabilitiesTotal;
  
  // Generate detailed breakdowns
  const balanceSheet: BalanceSheetYear = {
    year,
    yearType: 'Current Year (Actual)',
    
    // ASSETS
    assets: {
      currentAssets: {
        cashAndBankBalance: Math.round(currentAssetsTotal * OPTIMAL_RATIOS.cashAndBank),
        stock: Math.round(currentAssetsTotal * OPTIMAL_RATIOS.stock),
        receivables: Math.round(currentAssetsTotal * OPTIMAL_RATIOS.receivables),
        shortTermAdvance: Math.round(currentAssetsTotal * 0.05),
        otherCurrentAsset: Math.round(currentAssetsTotal * 0.03),
        depositAdvances: Math.round(currentAssetsTotal * 0.04),
        others: Math.round(currentAssetsTotal * 0.03),
      },
      
      fixedAssets: {
        landAndBuilding: Math.round(fixedAssetsTotal * OPTIMAL_RATIOS.landBuilding),
        plantMachinery: Math.round(fixedAssetsTotal * OPTIMAL_RATIOS.plantMachinery),
        fittingsFurniture: Math.round(fixedAssetsTotal * 0.05),
        vehicles: Math.round(fixedAssetsTotal * 0.08),
        officeEquipments: Math.round(fixedAssetsTotal * 0.04),
        advanceAgainstCapitalGoods: Math.round(fixedAssetsTotal * 0.03),
      },
      
      nonCurrentAssets: {
        investment: Math.round(nonCurrentAssetsTotal * 0.80),
        deferredTaxOthers: Math.round(nonCurrentAssetsTotal * 0.20),
      },
      
      others: {
        otherAdvance: Math.round(othersAssetsTotal * 0.40),
        buildingDepositOtherDeposit: Math.round(othersAssetsTotal * 0.35),
        inventoryProject: Math.round(othersAssetsTotal * 0.25),
      },
    },
    
    // LIABILITIES
    liabilities: {
      currentLiabilities: {
        tradeCreditors: Math.round(currentLiabilitiesTotal * OPTIMAL_RATIOS.tradeCreditors),
        expensesDutyOtherPayables: Math.round(currentLiabilitiesTotal * 0.08),
        lcCreditorsJobWork: Math.round(currentLiabilitiesTotal * 0.05),
        advanceReceivedFromBuyers: Math.round(currentLiabilitiesTotal * 0.04),
        shortTermBankFinance: Math.round(currentLiabilitiesTotal * OPTIMAL_RATIOS.bankFinance),
        provisions: Math.round(currentLiabilitiesTotal * 0.03),
        taxPayable: Math.round(currentLiabilitiesTotal * 0.06),
        commercialPaper: Math.round(currentLiabilitiesTotal * 0.02),
        sdMaturityRepayableOneYear: Math.round(currentLiabilitiesTotal * 0.02),
      },
      
      branchSisterConcerns: {
        havingCreditFacilities: Math.round(balancedTotal * 0.02), // 2% of total
        notHavingFacilities: Math.round(balancedTotal * 0.01), // 1% of total
      },
      
      longTermLiabilities: {
        longTermFundsFromFamily: Math.round(longTermLiabilitiesTotal * 0.15),
        longTermLoanFromBank: Math.round(longTermLiabilitiesTotal * 0.60),
        longTermLoanFromInstitutions: Math.round(longTermLiabilitiesTotal * 0.20),
        cautionDeposit: Math.round(longTermLiabilitiesTotal * 0.05),
      },
      
      capitalAndReserve: {
        paidUpCapital: Math.round(capitalReservesTotal * OPTIMAL_RATIOS.paidUpCapital),
        reservesAndSurplus: Math.round(capitalReservesTotal * OPTIMAL_RATIOS.reservesSurplus),
        convertiblePreferenceShare: Math.round(capitalReservesTotal * 0.05),
        plAccount: Math.round(capitalReservesTotal * OPTIMAL_RATIOS.retainedEarnings),
        lessMiscExpensesDrawings: Math.round(capitalReservesTotal * 0.05),
      },
    },
    
    // PROFIT & LOSS (Estimated based on assets)
    profitLoss: {
      grossSalesDomestic: Math.round(balancedTotal * 1.2), // 120% of assets (good turnover)
      exportSales: Math.round(balancedTotal * 0.3), // 30% of assets
      commissionSales: Math.round(balancedTotal * 0.05), // 5% of assets
      interest: Math.round(balancedTotal * 0.08), // 8% interest expense
      tax: Math.round(balancedTotal * 0.06), // 6% tax
      depreciation: Math.round(fixedAssetsTotal * 0.10), // 10% of fixed assets
      netProfit: Math.round(balancedTotal * 0.12), // 12% net profit margin
    },
  };
  
  // Ensure totals match exactly by adjusting the largest components
  adjustToExactTotal(balanceSheet, balancedTotal);
  
  return balanceSheet;
}

function adjustToExactTotal(balanceSheet: BalanceSheetYear, targetTotal: number) {
  // Calculate current totals
  const currentAssetsTotal = Object.values(balanceSheet.assets.currentAssets).reduce((sum, val) => sum + val, 0);
  const fixedAssetsTotal = Object.values(balanceSheet.assets.fixedAssets).reduce((sum, val) => sum + val, 0);
  const nonCurrentAssetsTotal = Object.values(balanceSheet.assets.nonCurrentAssets).reduce((sum, val) => sum + val, 0);
  const othersAssetsTotal = Object.values(balanceSheet.assets.others).reduce((sum, val) => sum + val, 0);
  
  const currentLiabilitiesTotal = Object.values(balanceSheet.liabilities.currentLiabilities).reduce((sum, val) => sum + val, 0);
  const branchSisterTotal = Object.values(balanceSheet.liabilities.branchSisterConcerns).reduce((sum, val) => sum + val, 0);
  const longTermLiabilitiesTotal = Object.values(balanceSheet.liabilities.longTermLiabilities).reduce((sum, val) => sum + val, 0);
  const capitalReservesTotal = Object.values(balanceSheet.liabilities.capitalAndReserve).reduce((sum, val) => sum + val, 0);
  
  const actualAssetsTotal = currentAssetsTotal + fixedAssetsTotal + nonCurrentAssetsTotal + othersAssetsTotal;
  const actualLiabilitiesTotal = currentLiabilitiesTotal + branchSisterTotal + longTermLiabilitiesTotal + capitalReservesTotal;
  
  // Adjust assets to match target
  const assetsDifference = targetTotal - actualAssetsTotal;
  if (assetsDifference !== 0) {
    balanceSheet.assets.currentAssets.cashAndBankBalance += assetsDifference;
  }
  
  // Adjust liabilities to match target
  const liabilitiesDifference = targetTotal - actualLiabilitiesTotal;
  if (liabilitiesDifference !== 0) {
    balanceSheet.liabilities.capitalAndReserve.reservesAndSurplus += liabilitiesDifference;
  }
}

export function calculateFinancialRatios(balanceSheet: BalanceSheetYear) {
  const currentAssets = Object.values(balanceSheet.assets.currentAssets).reduce((sum, val) => sum + val, 0);
  const currentLiabilities = Object.values(balanceSheet.liabilities.currentLiabilities).reduce((sum, val) => sum + val, 0);
  const totalAssets = currentAssets + 
    Object.values(balanceSheet.assets.fixedAssets).reduce((sum, val) => sum + val, 0) +
    Object.values(balanceSheet.assets.nonCurrentAssets).reduce((sum, val) => sum + val, 0) +
    Object.values(balanceSheet.assets.others).reduce((sum, val) => sum + val, 0);
  
  const totalLiabilities = currentLiabilities +
    Object.values(balanceSheet.liabilities.branchSisterConcerns).reduce((sum, val) => sum + val, 0) +
    Object.values(balanceSheet.liabilities.longTermLiabilities).reduce((sum, val) => sum + val, 0);
  
  const equity = Object.values(balanceSheet.liabilities.capitalAndReserve).reduce((sum, val) => sum + val, 0);
  
  return {
    currentRatio: currentLiabilities > 0 ? (currentAssets / currentLiabilities).toFixed(2) : 'N/A',
    debtToEquityRatio: equity > 0 ? (totalLiabilities / equity).toFixed(2) : 'N/A',
    workingCapital: (currentAssets - currentLiabilities).toLocaleString('en-IN'),
    totalAssets: totalAssets.toLocaleString('en-IN'),
    totalLiabilities: (totalLiabilities + equity).toLocaleString('en-IN'),
    equityRatio: totalAssets > 0 ? ((equity / totalAssets) * 100).toFixed(1) + '%' : 'N/A',
  };
}
