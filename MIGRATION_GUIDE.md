# Migration Guide: localStorage → Supabase Backend

## 🎯 Overview

This guide helps you migrate your Zheet.io application from localStorage-based data storage to a proper Supabase backend with PostgreSQL database, authentication, and file storage.

## 📋 What's Been Created

### 1. **Database Schema** (`database/schema.sql`)
- ✅ Users table with subscription management
- ✅ Companies table for business information
- ✅ Balance sheet analyses tracking
- ✅ Balance sheet data storage (yearly data)
- ✅ File upload tracking
- ✅ Activity logging for analytics
- ✅ Payment transactions (for Stripe integration)
- ✅ Row Level Security (RLS) policies
- ✅ Storage bucket for file uploads

### 2. **API Services** (`src/services/api.ts`)
- ✅ `AuthService` - User registration, login, logout
- ✅ `CompanyService` - Company creation and management
- ✅ `AnalysisService` - Balance sheet analysis operations
- ✅ `FileService` - File upload and management
- ✅ `ActivityService` - User activity tracking

### 3. **Authentication Hook** (`src/hooks/useAuth.ts`)
- ✅ Modern React hook for authentication state
- ✅ Automatic session management
- ✅ Subscription feature checking
- ✅ Test user support

### 4. **Supabase Configuration** (`src/lib/supabase.ts`)
- ✅ Supabase client setup
- ✅ TypeScript database types
- ✅ Authentication configuration

## 🔄 Migration Steps

### Step 1: Set Up Supabase (Follow BACKEND_SETUP_GUIDE.md)
1. Create Supabase project
2. Run database schema
3. Configure environment variables
4. Set up email authentication
5. Create test user account

### Step 2: Update Authentication Flow

Replace the current localStorage-based authentication in your components:

**Before (localStorage):**
```typescript
import { createSession, getSession, clearSession } from "./utils/sessionManager";
```

**After (Supabase):**
```typescript
import { useAuth } from "./hooks/useAuth";

const { user, isAuthenticated, signIn, signOut, isLoading } = useAuth();
```

### Step 3: Update Company Management

**Before (localStorage):**
```typescript
localStorage.setItem("companyInfo", JSON.stringify(company));
const savedCompany = localStorage.getItem("companyInfo");
```

**After (Supabase):**
```typescript
import { CompanyService } from "./services/api";

const { company, error } = await CompanyService.createCompany(companyData);
const { companies } = await CompanyService.getUserCompanies();
```

### Step 4: Update Balance Sheet Data Storage

**Before (localStorage):**
```typescript
localStorage.setItem("balanceSheetData", JSON.stringify(allData));
const savedData = localStorage.getItem("balanceSheetData");
```

**After (Supabase):**
```typescript
import { AnalysisService } from "./services/api";

// Create analysis
const { analysisId } = await AnalysisService.createAnalysis(companyId, "Analysis Name", "manual");

// Save yearly data
await AnalysisService.saveBalanceSheetData(analysisId, yearData);

// Retrieve data
const { data } = await AnalysisService.getAnalysisData(analysisId);
```

## 🔧 Component Updates Needed

### 1. Update App.tsx

Replace localStorage session management with the new useAuth hook:

```typescript
// Remove old imports
// import { createSession, getSession, clearSession } from "./utils/sessionManager";

// Add new imports
import { useAuth } from "./hooks/useAuth";

function App() {
  const { user, isAuthenticated, signIn, signOut, isLoading } = useAuth();
  
  // Remove localStorage-based state management
  // Replace with Supabase-based operations
}
```

### 2. Update CompanySetup.tsx

Replace localStorage company storage:

```typescript
import { CompanyService } from "../services/api";

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  if (validateForm()) {
    const { company, error } = await CompanyService.createCompany(companyInfo);
    
    if (error) {
      setError(error);
      return;
    }
    
    onCompanySetup(company);
  }
};
```

### 3. Update HorizontalBalanceSheet.tsx

Replace localStorage data saving:

```typescript
import { AnalysisService } from "../services/api";

const handleSaveAndNext = async () => {
  // Create analysis if not exists
  if (!analysisId) {
    const { analysisId: newAnalysisId, error } = await AnalysisService.createAnalysis(
      company.id!,
      `${company.companyName} Analysis`,
      "manual"
    );
    
    if (error) {
      setError(error);
      return;
    }
    
    setAnalysisId(newAnalysisId);
  }
  
  // Save year data
  const { error } = await AnalysisService.saveBalanceSheetData(analysisId, yearData);
  
  if (error) {
    setError(error);
    return;
  }
  
  // Continue with next year or complete
};
```

### 4. Update Authentication Components

**LoginPage.tsx & RegisterPage.tsx:**
```typescript
import { useAuth } from "../hooks/useAuth";

const LoginPage = () => {
  const { signIn, isLoading, error } = useAuth();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const { success, error } = await signIn(credentials);
    
    if (success) {
      onLoginSuccess();
    }
  };
};
```

## 🧪 Testing the Migration

### 1. Test User Registration
```typescript
const testRegistration = async () => {
  const { success, error } = await signUp({
    email: "<EMAIL>",
    password: "TestPassword123!",
    firstName: "Test",
    lastName: "User",
    company: "Test Company",
    phone: "1234567890"
  });
  
  console.log("Registration:", { success, error });
};
```

### 2. Test Company Creation
```typescript
const testCompanyCreation = async () => {
  const { company, error } = await CompanyService.createCompany({
    companyName: "Test Company Ltd",
    address: "123 Test Street",
    contactPerson: "John Doe",
    phone: "9876543210",
    email: "<EMAIL>",
    panNumber: "**********",
    gstNumber: "12**********1Z5",
    numberOfYears: 3
  });
  
  console.log("Company creation:", { company, error });
};
```

### 3. Test Data Persistence
```typescript
const testDataPersistence = async () => {
  // Create analysis
  const { analysisId } = await AnalysisService.createAnalysis(companyId, "Test Analysis", "manual");
  
  // Save data
  await AnalysisService.saveBalanceSheetData(analysisId, yearData);
  
  // Retrieve data
  const { data } = await AnalysisService.getAnalysisData(analysisId);
  
  console.log("Data persistence:", { analysisId, data });
};
```

## 🚨 Important Notes

### 1. **Data Migration**
- Existing localStorage data will be lost during migration
- Consider creating a one-time migration script if you have important test data
- Users will need to re-register and re-enter their data

### 2. **Environment Variables**
- Never commit `.env` file to version control
- Use different Supabase projects for development and production
- Rotate API keys regularly

### 3. **Error Handling**
- All API calls now return `{ data, error }` objects
- Always check for errors before proceeding
- Implement proper loading states

### 4. **Subscription Limits**
- Free tier users are limited to 1 analysis
- Implement proper usage checking before creating analyses
- Show upgrade prompts when limits are reached

## 🔄 Rollback Plan

If you need to rollback to localStorage:

1. Keep the old `sessionManager.ts` file
2. Revert component changes
3. Comment out Supabase imports
4. Re-enable localStorage operations

## 📞 Support

If you encounter issues during migration:

1. Check Supabase dashboard logs
2. Verify environment variables
3. Test API endpoints in Supabase SQL editor
4. Check browser console for errors

---

## ✅ Migration Checklist

- [ ] Supabase project created and configured
- [ ] Database schema deployed
- [ ] Environment variables set
- [ ] Test user account created
- [ ] App.tsx updated with useAuth hook
- [ ] CompanySetup.tsx updated with CompanyService
- [ ] HorizontalBalanceSheet.tsx updated with AnalysisService
- [ ] Authentication components updated
- [ ] File upload components updated (if applicable)
- [ ] Error handling implemented
- [ ] Loading states added
- [ ] Testing completed
- [ ] Production deployment ready

Once migration is complete, you'll have a robust, scalable backend ready for the next phase: **Payment Processing Integration**!
