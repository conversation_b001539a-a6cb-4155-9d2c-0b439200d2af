.horizontal-balance-sheet {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 2rem;
}

.balance-sheet-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.back-to-options {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background-color: #27ae60;
  border: 2px solid #27ae60;
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-to-options:hover {
  background-color: #229954;
  border-color: #229954;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
}

.company-info h1 {
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.company-info p {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
}

.year-info {
  text-align: right;
}

.year-info h2 {
  color: #34495e;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.year-info h3 {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

.year-info .progress-indicator {
  margin-top: 1rem;
  background: none;
  padding: 0;
  box-shadow: none;
}

.year-info .progress-text {
  font-size: 0.85rem;
  color: #7f8c8d;
  font-weight: 500;
  display: block;
  margin-bottom: 0.5rem;
  text-align: right;
}

.year-info .progress-bar {
  width: 100%;
  height: 4px;
  background: #e1e8ed;
  border-radius: 2px;
  overflow: hidden;
}

.year-info .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-indicator {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.progress-steps::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background: #e1e8ed;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: white;
  padding: 0 1rem;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e1e8ed;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step.completed .step-number {
  background: #27ae60;
  color: white;
}

.step.active .step-number {
  background: #3498db;
  color: white;
  box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.2);
}

.step-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: center;
  font-weight: 500;
}

.step.active .step-label {
  color: #3498db;
  font-weight: 600;
}

.balance-sheet-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.balance-sheet-side {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.side-title {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 3px solid #3498db;
}

.balance-sheet-table {
  width: 100%;
  border-collapse: collapse;
}

.balance-sheet-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ecf0f1;
}

.section-header td {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  border-top: 2px solid #3498db;
  border-bottom: 2px solid #3498db;
}

.field-label {
  font-weight: 500;
  color: #2c3e50;
  width: 70%;
}

.amount-cell {
  width: 30%;
  text-align: right;
}

.amount-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  text-align: right;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.amount-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.section-total td {
  background-color: #e8f4f8;
  font-weight: 600;
  color: #2c3e50;
  border-top: 2px solid #3498db;
  border-bottom: 2px solid #3498db;
}

.grand-total td {
  background-color: #d5e8d4;
  font-weight: 700;
  color: #2c3e50;
  border-top: 3px solid #27ae60;
  border-bottom: 3px solid #27ae60;
  font-size: 1.1rem;
  padding: 1rem;
}

.navigation-controls {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
  border: 2px solid #bdc3c7;
}

.btn-secondary:hover:not(:disabled) {
  background: #d5dbdb;
  transform: translateY(-2px);
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.year-indicator {
  font-weight: 600;
  color: #7f8c8d;
  font-size: 1rem;
}

@media (max-width: 1024px) {
  .balance-sheet-content {
    grid-template-columns: 1fr;
  }

  .balance-sheet-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .year-info {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .horizontal-balance-sheet {
    padding: 1rem;
  }

  .balance-sheet-header,
  .balance-sheet-side,
  .navigation-controls {
    padding: 1rem;
  }

  .progress-steps {
    flex-direction: column;
    gap: 1rem;
  }

  .progress-steps::before {
    display: none;
  }

  .step {
    flex-direction: row;
    gap: 1rem;
  }

  .navigation-controls {
    flex-direction: column;
    gap: 1rem;
  }
}
