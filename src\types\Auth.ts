export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "user" | "manager" | "super_admin";
  subscription: SubscriptionPlan;
  isEmailVerified: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
  profileImage?: string;
  company?: string;
  phone?: string;
}

export interface SubscriptionPlan {
  type: "free" | "premium" | "lifetime";
  status: "active" | "expired" | "cancelled";
  startDate: Date;
  endDate?: Date;
  features: SubscriptionFeatures;
  usageCount: number;
  maxUsage: number;
}

export interface SubscriptionFeatures {
  maxAnalyses: number;
  maxCompanies: number;
  exportToExcel: boolean;
  prioritySupport: boolean;
  advancedAnalytics: boolean;
  apiAccess: boolean;
  customReports: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  company?: string;
  phone?: string;
}

export interface EmailVerificationData {
  email: string;
  verificationCode: string;
}

export interface PasswordResetData {
  email: string;
  resetCode: string;
  newPassword: string;
}

// Subscription Plans Configuration
export const SUBSCRIPTION_PLANS = {
  free: {
    name: "Free Trial",
    price: 0,
    duration: "1 Day",
    features: {
      maxAnalyses: 1,
      maxCompanies: 1,
      exportToExcel: false,
      prioritySupport: false,
      advancedAnalytics: false,
      apiAccess: false,
      customReports: false,
    },
    description: "Try our platform for free",
    popular: false,
  },
  premium: {
    name: "Premium Monthly",
    price: 49,
    duration: "per month",
    features: {
      maxAnalyses: 100,
      maxCompanies: 10,
      exportToExcel: true,
      prioritySupport: true,
      advancedAnalytics: true,
      apiAccess: false,
      customReports: true,
    },
    description: "Perfect for growing businesses",
    popular: true,
  },
  lifetime: {
    name: "Lifetime Access",
    price: 100, // Launch offer
    originalPrice: 999,
    duration: "one-time",
    features: {
      maxAnalyses: -1, // Unlimited
      maxCompanies: -1, // Unlimited
      exportToExcel: true,
      prioritySupport: true,
      advancedAnalytics: true,
      apiAccess: true,
      customReports: true,
    },
    description: "Complete access forever",
    popular: false,
    isLaunchOffer: true,
  },
} as const;

export type SubscriptionPlanType = keyof typeof SUBSCRIPTION_PLANS;

// Test User Configuration
export const TEST_USER_CREDENTIALS = {
  email: "vivek.s.rajiv",
  password: "Tester@Wholesite",
} as const;

// Test User Profile with Full Access
export const TEST_USER_PROFILE: User = {
  id: "test-user-vivek",
  email: "vivek.s.rajiv",
  firstName: "Vivek",
  lastName: "Rajiv",
  role: "super_admin",
  subscription: {
    type: "lifetime",
    status: "active",
    startDate: new Date("2024-01-01"),
    endDate: undefined, // Lifetime access
    features: {
      maxAnalyses: -1, // Unlimited
      maxCompanies: -1, // Unlimited
      exportToExcel: true,
      prioritySupport: true,
      advancedAnalytics: true,
      apiAccess: true,
      customReports: true,
    },
    usageCount: 0,
    maxUsage: -1, // Unlimited
  },
  isEmailVerified: true,
  createdAt: new Date("2024-01-01"),
  lastLoginAt: new Date(),
  company: "FinanceAI Pro Testing",
  phone: "9786470779",
} as const;

// Admin Dashboard Types
export interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  analysesPerformed: number;
  newUsersToday: number;
  conversionRate: number;
}

export interface AdminUser extends User {
  subscriptionHistory: SubscriptionPlan[];
  totalAnalyses: number;
  lastActivity: Date;
  registrationSource: string;
}

// Analytics Types
export interface UsageAnalytics {
  date: string;
  analyses: number;
  newUsers: number;
  revenue: number;
  activeUsers: number;
}

export interface FeatureUsage {
  feature: string;
  usageCount: number;
  userCount: number;
  conversionRate: number;
}
