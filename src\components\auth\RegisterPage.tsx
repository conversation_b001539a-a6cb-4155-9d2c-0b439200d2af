import React, { useState } from "react";
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  User,
  Building,
  Phone,
  BarChart3,
  CheckCircle,
  Gift,
} from "lucide-react";
import { RegisterData } from "../../types/Auth";
import "./AuthPages.css";

interface Props {
  onRegister: (data: RegisterData) => Promise<void>;
  onLogin: () => void;
  onBackToLanding: () => void;
  isLoading?: boolean;
  error?: string;
}

const RegisterPage: React.FC<Props> = ({
  onRegister,
  onLogin,
  onBackToLanding,
  isLoading = false,
  error,
}) => {
  const [formData, setFormData] = useState<RegisterData>({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    company: "",
    phone: "",
  });
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    } else if (formData.password.length < 8) {
      errors.password = "Password must be at least 8 characters";
    }

    if (formData.password !== confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    if (!acceptTerms) {
      errors.terms = "You must accept the terms and conditions";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      await onRegister(formData);
    }
  };

  const handleInputChange = (field: keyof RegisterData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <div className="auth-page">
      <div className="auth-container">
        {/* Left Side - Branding */}
        <div className="auth-branding">
          <div className="branding-content">
            <div className="brand-logo">
              <BarChart3 className="brand-icon" />
              <span className="brand-name">Zheet.io</span>
            </div>

            <div className="launch-offer-card">
              <div className="offer-badge">
                <Gift className="offer-icon" />
                <span>LAUNCH OFFER</span>
              </div>
              <h2 className="offer-title">Lifetime Access</h2>
              <div className="offer-price">
                <span className="current-price">$100</span>
                <span className="original-price">$999</span>
              </div>
              <p className="offer-description">
                Get unlimited access to all features forever. Limited time
                offer!
              </p>
              <div className="offer-features">
                <div className="offer-feature">
                  <CheckCircle className="feature-check" />
                  <span>Unlimited Analyses</span>
                </div>
                <div className="offer-feature">
                  <CheckCircle className="feature-check" />
                  <span>Excel Export</span>
                </div>
                <div className="offer-feature">
                  <CheckCircle className="feature-check" />
                  <span>Priority Support</span>
                </div>
                <div className="offer-feature">
                  <CheckCircle className="feature-check" />
                  <span>API Access</span>
                </div>
              </div>
            </div>

            <div className="testimonial-mini">
              <p className="testimonial-text">
                "Zheet.io transformed our analysis process. The accuracy is
                incredible!"
              </p>
              <div className="testimonial-author">
                <span className="author-name">Sarah Johnson</span>
                <span className="author-title">CFO, TechCorp</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Register Form */}
        <div className="auth-form-section">
          <div className="auth-form-container">
            <div className="auth-header">
              <button onClick={onBackToLanding} className="back-to-landing">
                ← Back to Home
              </button>
              <h2 className="auth-title">Create Account</h2>
              <p className="auth-subtitle">
                Start your free trial and experience the power of AI-driven
                financial analysis
              </p>
            </div>

            {error && (
              <div className="auth-error">
                <span>{error}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="auth-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="firstName" className="form-label">
                    First Name *
                  </label>
                  <div className="input-wrapper">
                    <User className="input-icon" />
                    <input
                      id="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={(e) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      placeholder="John"
                      className={`form-input ${
                        formErrors.firstName ? "error" : ""
                      }`}
                      required
                    />
                  </div>
                  {formErrors.firstName && (
                    <span className="field-error">{formErrors.firstName}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="lastName" className="form-label">
                    Last Name *
                  </label>
                  <div className="input-wrapper">
                    <User className="input-icon" />
                    <input
                      id="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={(e) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      placeholder="Doe"
                      className={`form-input ${
                        formErrors.lastName ? "error" : ""
                      }`}
                      required
                    />
                  </div>
                  {formErrors.lastName && (
                    <span className="field-error">{formErrors.lastName}</span>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  Email Address *
                </label>
                <div className="input-wrapper">
                  <Mail className="input-icon" />
                  <input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="<EMAIL>"
                    className={`form-input ${formErrors.email ? "error" : ""}`}
                    required
                  />
                </div>
                {formErrors.email && (
                  <span className="field-error">{formErrors.email}</span>
                )}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="company" className="form-label">
                    Company
                  </label>
                  <div className="input-wrapper">
                    <Building className="input-icon" />
                    <input
                      id="company"
                      type="text"
                      value={formData.company}
                      onChange={(e) =>
                        handleInputChange("company", e.target.value)
                      }
                      placeholder="Your Company"
                      className="form-input"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="phone" className="form-label">
                    Phone
                  </label>
                  <div className="input-wrapper">
                    <Phone className="input-icon" />
                    <input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) =>
                        handleInputChange("phone", e.target.value)
                      }
                      placeholder="+****************"
                      className="form-input"
                    />
                  </div>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Password *
                </label>
                <div className="input-wrapper">
                  <Lock className="input-icon" />
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    placeholder="Create a strong password"
                    className={`form-input ${
                      formErrors.password ? "error" : ""
                    }`}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                {formErrors.password && (
                  <span className="field-error">{formErrors.password}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword" className="form-label">
                  Confirm Password *
                </label>
                <div className="input-wrapper">
                  <Lock className="input-icon" />
                  <input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    className={`form-input ${
                      formErrors.confirmPassword ? "error" : ""
                    }`}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="password-toggle"
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={20} />
                    ) : (
                      <Eye size={20} />
                    )}
                  </button>
                </div>
                {formErrors.confirmPassword && (
                  <span className="field-error">
                    {formErrors.confirmPassword}
                  </span>
                )}
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={acceptTerms}
                    onChange={(e) => setAcceptTerms(e.target.checked)}
                    className="checkbox-input"
                  />
                  <span className="checkbox-text">
                    I agree to the{" "}
                    <a href="#terms" className="checkbox-link">
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a href="#privacy" className="checkbox-link">
                      Privacy Policy
                    </a>
                  </span>
                </label>
                {formErrors.terms && (
                  <span className="field-error">{formErrors.terms}</span>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="auth-submit-btn"
              >
                {isLoading ? (
                  <span className="loading-spinner"></span>
                ) : (
                  <>
                    Create Account
                    <ArrowRight className="btn-icon" />
                  </>
                )}
              </button>
            </form>

            <div className="auth-divider">
              <span>or</span>
            </div>

            <div className="auth-alternative">
              <p className="alternative-text">Already have an account?</p>
              <button onClick={onLogin} className="alternative-btn">
                Sign In
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
