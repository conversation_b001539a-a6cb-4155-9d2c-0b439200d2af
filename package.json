{"name": "balance-sheet-analyzer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@types/file-saver": "^2.0.7", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.5.1", "file-saver": "^2.0.5", "lucide-react": "^0.514.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "xlsx": "^0.18.5"}}