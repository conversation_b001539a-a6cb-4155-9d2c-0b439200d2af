import { User } from '../types/Auth';

const SESSION_KEY = 'financeai_session';
const SESSION_EXPIRY_KEY = 'financeai_session_expiry';
const ACTIVITY_KEY = 'financeai_last_activity';

// Session duration in milliseconds (24 hours)
const SESSION_DURATION = 24 * 60 * 60 * 1000;

// Activity timeout in milliseconds (2 hours of inactivity)
const ACTIVITY_TIMEOUT = 2 * 60 * 60 * 1000;

export interface SessionData {
  user: User;
  loginTime: number;
  lastActivity: number;
  expiresAt: number;
}

/**
 * Create a new session for the user
 */
export const createSession = (user: User): void => {
  const now = Date.now();
  const sessionData: SessionData = {
    user,
    loginTime: now,
    lastActivity: now,
    expiresAt: now + SESSION_DURATION
  };

  localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
  localStorage.setItem(SESSION_EXPIRY_KEY, sessionData.expiresAt.toString());
  localStorage.setItem(ACTIVITY_KEY, now.toString());

  console.log('🔐 Session created for user:', user.email);
};

/**
 * Get current session data
 */
export const getSession = (): SessionData | null => {
  try {
    const sessionStr = localStorage.getItem(SESSION_KEY);
    if (!sessionStr) return null;

    const session: SessionData = JSON.parse(sessionStr);
    const now = Date.now();

    // Check if session has expired
    if (now > session.expiresAt) {
      console.log('⏰ Session expired');
      clearSession();
      return null;
    }

    // Check for inactivity timeout
    if (now - session.lastActivity > ACTIVITY_TIMEOUT) {
      console.log('💤 Session expired due to inactivity');
      clearSession();
      return null;
    }

    return session;
  } catch (error) {
    console.error('Error reading session:', error);
    clearSession();
    return null;
  }
};

/**
 * Update last activity timestamp
 */
export const updateActivity = (): void => {
  const session = getSession();
  if (!session) return;

  const now = Date.now();
  session.lastActivity = now;

  localStorage.setItem(SESSION_KEY, JSON.stringify(session));
  localStorage.setItem(ACTIVITY_KEY, now.toString());
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  return getSession() !== null;
};

/**
 * Get current user from session
 */
export const getCurrentUser = (): User | null => {
  const session = getSession();
  return session ? session.user : null;
};

/**
 * Clear session data
 */
export const clearSession = (): void => {
  localStorage.removeItem(SESSION_KEY);
  localStorage.removeItem(SESSION_EXPIRY_KEY);
  localStorage.removeItem(ACTIVITY_KEY);
  console.log('🚪 Session cleared');
};

/**
 * Extend session expiry
 */
export const extendSession = (): void => {
  const session = getSession();
  if (!session) return;

  const now = Date.now();
  session.expiresAt = now + SESSION_DURATION;
  session.lastActivity = now;

  localStorage.setItem(SESSION_KEY, JSON.stringify(session));
  localStorage.setItem(SESSION_EXPIRY_KEY, session.expiresAt.toString());
  localStorage.setItem(ACTIVITY_KEY, now.toString());

  console.log('⏰ Session extended');
};

/**
 * Get session info for display
 */
export const getSessionInfo = (): {
  isActive: boolean;
  timeRemaining: number;
  lastActivity: number;
} => {
  const session = getSession();
  if (!session) {
    return { isActive: false, timeRemaining: 0, lastActivity: 0 };
  }

  const now = Date.now();
  return {
    isActive: true,
    timeRemaining: Math.max(0, session.expiresAt - now),
    lastActivity: session.lastActivity
  };
};

/**
 * Initialize session monitoring
 */
export const initSessionMonitoring = (onSessionExpired: () => void): () => void => {
  // Update activity on user interactions
  const updateActivityHandler = () => updateActivity();
  
  // Add event listeners for user activity
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
  events.forEach(event => {
    document.addEventListener(event, updateActivityHandler, true);
  });

  // Check session validity periodically
  const sessionCheckInterval = setInterval(() => {
    if (!isAuthenticated()) {
      onSessionExpired();
    }
  }, 60000); // Check every minute

  // Return cleanup function
  return () => {
    events.forEach(event => {
      document.removeEventListener(event, updateActivityHandler, true);
    });
    clearInterval(sessionCheckInterval);
  };
};

/**
 * Format time remaining for display
 */
export const formatTimeRemaining = (milliseconds: number): string => {
  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
};
