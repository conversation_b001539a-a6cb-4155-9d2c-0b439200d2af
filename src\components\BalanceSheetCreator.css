.balance-sheet-creator {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.creator-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.creator-header {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 2rem;
  top: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

.creator-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.creator-header h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.creator-header p {
  font-size: 1.1rem;
  opacity: 0.8;
  line-height: 1.6;
}

.totals-input {
  padding: 3rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.input-group input {
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.input-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.input-group input[type="number"] {
  text-align: right;
}

.balance-check {
  margin: 0 3rem 3rem 3rem;
  padding: 2rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.balance-check.balanced {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border: 2px solid #27ae60;
}

.balance-check.unbalanced {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  border: 2px solid #e74c3c;
}

.balance-icon {
  font-size: 2rem;
}

.balance-text {
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
}

.balance-amounts {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: #34495e;
}

.balance-sheet-preview {
  margin: 0 3rem 3rem 3rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid #27ae60;
}

.preview-header {
  text-align: center;
  margin-bottom: 2rem;
}

.preview-header h3 {
  color: #27ae60;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.ratios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.ratio-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ratio-label {
  font-weight: 600;
  color: #2c3e50;
}

.ratio-value {
  font-weight: 700;
  color: #27ae60;
}

.ratio-value.good {
  color: #27ae60;
}

.preview-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summary-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.summary-value {
  font-weight: 700;
  color: #27ae60;
  font-size: 1.2rem;
}

.key-highlights {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.key-highlights h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.key-highlights ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.key-highlights li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #ecf0f1;
  color: #34495e;
  line-height: 1.5;
}

.key-highlights li:last-child {
  border-bottom: none;
}

.export-options {
  padding: 0 3rem 3rem 3rem;
}

.export-options h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.export-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.export-btn {
  background: white;
  border: 3px solid #e1e8ed;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.export-btn:hover:not(:disabled) {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pdf-btn:hover:not(:disabled) {
  border-color: #e74c3c;
  box-shadow: 0 15px 30px rgba(231, 76, 60, 0.2);
}

.excel-btn:hover:not(:disabled) {
  border-color: #27ae60;
  box-shadow: 0 15px 30px rgba(39, 174, 96, 0.2);
}

.btn-icon {
  font-size: 3rem;
}

.btn-text {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
}

.btn-desc {
  font-size: 0.9rem;
  color: #7f8c8d;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .balance-sheet-creator {
    padding: 1rem;
  }

  .creator-container {
    margin: 0;
  }

  .creator-header {
    padding: 1.5rem;
  }

  .back-button {
    position: static;
    margin-bottom: 1rem;
  }

  .creator-header h1 {
    font-size: 2rem;
  }

  .totals-input {
    padding: 2rem;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .balance-check {
    margin: 0 2rem 2rem 2rem;
    flex-direction: column;
    text-align: center;
  }

  .export-options {
    padding: 0 2rem 2rem 2rem;
  }

  .export-buttons {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .export-btn {
    padding: 1.5rem;
  }
}
