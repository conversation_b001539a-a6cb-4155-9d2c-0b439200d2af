.landing-page {
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, sans-serif;
  scroll-behavior: smooth;
}

html {
  scroll-behavior: smooth;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(225, 232, 237, 0.8);
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  min-height: 80px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  min-height: 80px;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.5rem;
  color: #1a365d;
}

.logo-icon {
  color: #3182ce;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-menu a,
.nav-menu .nav-link {
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  position: relative;
}

.nav-menu a:hover,
.nav-menu .nav-link:hover {
  color: #3182ce;
  background: rgba(49, 130, 206, 0.1);
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-login {
  background: none;
  border: 1px solid #e2e8f0;
  color: #4a5568;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-login:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.btn-primary {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(49, 130, 206, 0.3);
}

/* Hero Section */
.hero-section {
  padding: 4rem 0 6rem;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  margin-top: 160px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.hero-right {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: #1a365d;
  margin-bottom: 1.5rem;
}

.highlight {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.hero-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2d3748;
  font-weight: 500;
}

.feature-icon {
  color: #38a169;
  flex-shrink: 0;
}

/* Inline Create Banner */
.inline-create-banner {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin: 2rem 0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.inline-create-banner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  padding: 2px;
  background: linear-gradient(135deg, #3498db, #27ae60);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.inline-banner-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: space-between;
}

.inline-banner-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inline-banner-icon-svg {
  width: 30px;
  height: 30px;
  color: white;
}

.inline-banner-text {
  flex: 1;
}

.inline-banner-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.inline-banner-description {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.inline-banner-cta {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white !important;
  border: none;
  border-radius: 10px;
  padding: 0.875rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  flex-shrink: 0;
}

.inline-banner-cta:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.inline-banner-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.inline-banner-cta:hover .inline-banner-arrow {
  transform: translateX(3px);
}

/* Right Side Create Banner */
.right-create-banner {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.right-create-banner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  padding: 2px;
  background: linear-gradient(135deg, #3498db, #27ae60);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.right-banner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.right-banner-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.right-banner-icon-svg {
  width: 30px;
  height: 30px;
  color: white;
}

.right-banner-text {
  flex: 1;
}

.right-banner-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.right-banner-description {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.right-banner-cta {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white !important;
  border: none;
  border-radius: 10px;
  padding: 0.875rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  width: 100%;
  justify-content: center;
}

.right-banner-cta:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.right-banner-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.right-banner-cta:hover .right-banner-arrow {
  transform: translateX(3px);
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.btn-hero-primary {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-hero-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(49, 130, 206, 0.4);
}

.btn-hero-secondary {
  background: white;
  border: 2px solid #e2e8f0;
  color: #4a5568;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
  border-color: #cbd5e0;
  transform: translateY(-2px);
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #3182ce;
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dashboard-preview {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 100%;
  max-width: 500px;
}

.preview-header {
  background: #f7fafc;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.preview-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #718096;
  cursor: pointer;
}

.tab.active {
  background: #3182ce;
  color: white;
}

.preview-content {
  padding: 2rem;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.chart-icon {
  color: #3182ce;
}

.chart-bars {
  display: flex;
  gap: 0.5rem;
  align-items: end;
  height: 100px;
}

.bar {
  width: 20px;
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border-radius: 4px 4px 0 0;
  animation: growUp 1s ease-out;
}

@keyframes growUp {
  from {
    height: 0;
  }
  to {
    height: var(--height);
  }
}

/* Create Banner Section */
.create-banner-section {
  padding: 3rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.create-banner {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.create-banner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 3px;
  background: linear-gradient(135deg, #3498db, #27ae60);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 2rem;
  justify-content: space-between;
}

.banner-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.banner-icon-svg {
  width: 40px;
  height: 40px;
  color: white;
}

.banner-text {
  flex: 1;
}

.banner-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.banner-description {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.banner-cta {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.banner-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.banner-arrow {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.banner-cta:hover .banner-arrow {
  transform: translateX(3px);
}

/* Features Section */
.features-section {
  padding: 6rem 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.25rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e0;
}

.feature-icon-wrapper {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.feature-card .feature-icon {
  color: white;
  size: 24px;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.feature-description {
  color: #4a5568;
  line-height: 1.6;
}

/* Social Proof */
.social-proof-section {
  padding: 4rem 0;
  background: #f7fafc;
}

.proof-stats {
  display: flex;
  justify-content: center;
  gap: 4rem;
}

.proof-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.proof-icon {
  color: #3182ce;
  size: 32px;
}

.proof-number {
  font-size: 2rem;
  font-weight: 800;
  color: #1a365d;
}

.proof-label {
  color: #718096;
  font-weight: 500;
}

/* Testimonials */
.testimonials-section {
  padding: 6rem 0;
  background: white;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.star-icon {
  color: #fbbf24;
  size: 16px;
}

.testimonial-text {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.author-name {
  font-weight: 600;
  color: #1a365d;
}

.author-title {
  color: #718096;
  font-size: 0.875rem;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, #1a365d, #2c5aa0);
  color: white;
  text-align: center;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-description {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.btn-cta-primary {
  background: white;
  border: none;
  color: #1a365d;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(255, 255, 255, 0.3);
}

.cta-note {
  margin-top: 1rem;
  opacity: 0.8;
  font-size: 0.875rem;
}

/* Footer */
.landing-footer {
  background: #1a202c;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-description {
  color: #a0aec0;
  margin-top: 1rem;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.link-group h4 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.link-group a {
  display: block;
  color: #a0aec0;
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.link-group a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid #2d3748;
  padding-top: 1rem;
  text-align: center;
  color: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .landing-header {
    min-height: auto;
    position: relative;
  }

  .header-content {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem 0;
    min-height: auto;
  }

  .nav-menu {
    display: flex;
    gap: 1rem;
    order: 2;
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-menu a,
  .nav-menu .nav-link {
    padding: 0.25rem 0.75rem;
    font-size: 0.9rem;
  }

  .header-actions {
    order: 3;
    gap: 0.5rem;
    justify-content: center;
  }

  .btn-login,
  .btn-primary {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .logo {
    order: 1;
    justify-self: center;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-right {
    order: -1;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-actions {
    flex-direction: column;
  }

  .hero-stats {
    justify-content: center;
  }

  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .banner-icon {
    width: 60px;
    height: 60px;
  }

  .banner-icon-svg {
    width: 30px;
    height: 30px;
  }

  .banner-title {
    font-size: 1.25rem;
  }

  .banner-description {
    font-size: 0.9rem;
  }

  .banner-cta {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .inline-banner-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .inline-banner-icon {
    width: 50px;
    height: 50px;
  }

  .inline-banner-icon-svg {
    width: 25px;
    height: 25px;
  }

  .inline-banner-title {
    font-size: 1.1rem;
  }

  .inline-banner-description {
    font-size: 0.85rem;
  }

  .inline-banner-cta {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
  }

  .launch-banner-wrapper {
    position: relative;
    margin-top: 0;
  }

  .hero-section {
    margin-top: 0;
  }

  .right-banner-icon {
    width: 50px;
    height: 50px;
  }

  .right-banner-icon-svg {
    width: 25px;
    height: 25px;
  }

  .right-banner-title {
    font-size: 1.1rem;
  }

  .right-banner-description {
    font-size: 0.85rem;
  }

  .right-banner-cta {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .proof-stats {
    flex-direction: column;
    gap: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-links {
    grid-template-columns: 1fr;
  }
}

/* Additional animations */
.btn-icon {
  transition: transform 0.3s ease;
}

.btn-hero-primary:hover .btn-icon,
.btn-cta-primary:hover .btn-icon,
.banner-cta:hover .btn-icon {
  transform: translateX(3px);
}
