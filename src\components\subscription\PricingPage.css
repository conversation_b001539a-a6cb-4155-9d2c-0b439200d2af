.pricing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.pricing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
.pricing-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  background: white;
  border: 1px solid #e2e8f0;
  color: #4a5568;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #f7fafc;
  transform: translateX(-3px);
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.5rem;
  color: #1a365d;
}

.brand-icon {
  color: #3182ce;
}

.pricing-title {
  font-size: 3rem;
  font-weight: 800;
  color: #1a365d;
  margin: 0;
}

.pricing-subtitle {
  font-size: 1.25rem;
  color: #4a5568;
  max-width: 600px;
}

/* Launch Banner */
.launch-banner {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 3rem;
  box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.banner-icon {
  size: 24px;
  flex-shrink: 0;
}

.banner-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.banner-title {
  font-weight: 700;
  font-size: 1.1rem;
}

.banner-description {
  opacity: 0.9;
}

.banner-timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

.timer-icon {
  size: 16px;
}

/* Pricing Grid */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.pricing-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.pricing-card.lifetime {
  border-color: #ff6b6b;
  background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.pricing-card.current {
  border-color: #3182ce;
  background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
}

.launch-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.launch-icon {
  size: 14px;
}

/* Card Header */
.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.plan-badge.free {
  background: #e6fffa;
  color: #00a3c4;
}

.plan-badge.premium {
  background: #fef5e7;
  color: #dd6b20;
}

.plan-badge.lifetime {
  background: #fed7d7;
  color: #c53030;
}

.badge-icon {
  size: 16px;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 1rem;
}

.plan-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.price-amount {
  font-size: 3rem;
  font-weight: 800;
  color: #1a365d;
  line-height: 1;
}

.price-original {
  font-size: 1.2rem;
  color: #a0aec0;
  text-decoration: line-through;
  margin-left: 0.5rem;
}

.price-period {
  color: #718096;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.savings-badge {
  background: #48bb78;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.plan-description {
  color: #4a5568;
  line-height: 1.5;
}

/* Features */
.card-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.feature-icon {
  flex-shrink: 0;
  size: 20px;
}

.feature-icon.included {
  color: #48bb78;
}

.feature-icon.excluded {
  color: #a0aec0;
}

/* Plan Buttons */
.plan-button {
  width: 100%;
  padding: 1rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.plan-button.free {
  background: #e6fffa;
  color: #00a3c4;
  border: 2px solid #00a3c4;
}

.plan-button.free:hover {
  background: #00a3c4;
  color: white;
}

.plan-button.premium {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  color: white;
}

.plan-button.premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(49, 130, 206, 0.3);
}

.plan-button.lifetime {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
}

.plan-button.lifetime:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
}

.plan-button.current {
  background: #e2e8f0;
  color: #4a5568;
  cursor: not-allowed;
}

.plan-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  size: 16px;
  transition: transform 0.3s ease;
}

.plan-button:hover .btn-icon {
  transform: translateX(3px);
}

/* Features Comparison */
.features-comparison {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin-bottom: 3rem;
}

.comparison-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 2rem;
}

.comparison-table {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
}

.table-header {
  display: contents;
}

.table-header > div {
  font-weight: 700;
  color: #1a365d;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
}

.table-row {
  display: contents;
}

.table-row > div {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-name {
  justify-content: flex-start !important;
  font-weight: 500;
  color: #2d3748;
}

.check-icon {
  color: #48bb78;
  size: 20px;
}

.x-icon {
  color: #a0aec0;
  size: 20px;
}

/* Trust Section */
.trust-section {
  text-align: center;
}

.trust-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 2rem;
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.trust-icon {
  color: #3182ce;
  size: 32px;
}

.trust-number {
  font-size: 2rem;
  font-weight: 800;
  color: #1a365d;
}

.trust-label {
  color: #718096;
  font-weight: 500;
}

.guarantee {
  background: #f0fff4;
  border: 2px solid #68d391;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  margin: 0 auto;
}

.guarantee h3 {
  color: #22543d;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.guarantee p {
  color: #2f855a;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pricing-container {
    padding: 1rem;
  }
  
  .pricing-title {
    font-size: 2rem;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .banner-content {
    flex-direction: column;
    text-align: center;
  }
  
  .comparison-table {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    background: white;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .trust-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .back-button {
    position: static;
    margin-bottom: 1rem;
  }
}
