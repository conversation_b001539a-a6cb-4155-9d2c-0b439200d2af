import React, { useState } from "react";
import { CompanyInfo } from "../types/FinancialData";
import "./CompanySetup.css";

interface Props {
  onCompanySetup: (company: CompanyInfo) => void;
  onBack?: () => void;
  showBackButton?: boolean;
}

const CompanySetup: React.FC<Props> = ({
  onCompanySetup,
  onBack,
  showBackButton = false,
}) => {
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    companyName: "",
    address: "",
    contactPerson: "",
    phone: "",
    email: "",
    panNumber: "",
    gstNumber: "",
    numberOfYears: 3, // Default to 3 years
  });

  const [errors, setErrors] = useState<Partial<CompanyInfo>>({});

  const handleInputChange = (
    field: keyof CompanyInfo,
    value: string | number
  ) => {
    setCompanyInfo((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CompanyInfo> = {};

    if (!companyInfo.companyName.trim()) {
      newErrors.companyName = "Company name is required";
    }

    if (!companyInfo.contactPerson.trim()) {
      newErrors.contactPerson = "Contact person is required";
    }

    if (!companyInfo.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    if (!companyInfo.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(companyInfo.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      const company: CompanyInfo = {
        ...companyInfo,
        id: Date.now().toString(), // Simple ID generation
        createdAt: new Date(),
      };

      // Store in localStorage for now (will be replaced with proper database)
      localStorage.setItem("companyInfo", JSON.stringify(company));

      onCompanySetup(company);
    }
  };

  return (
    <div className="company-setup">
      <div className="company-setup-container">
        <div className="company-setup-header">
          {showBackButton && onBack && (
            <button onClick={onBack} className="back-to-home">
              ← Back to Home
            </button>
          )}
          <h1>Balance Sheet Analyzer</h1>
          <h2>Company Information Setup</h2>
          <p>
            Please provide your company details to get started with financial
            analysis
          </p>
        </div>

        <form onSubmit={handleSubmit} className="company-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="companyName">Company Name *</label>
              <input
                type="text"
                id="companyName"
                value={companyInfo.companyName}
                onChange={(e) =>
                  handleInputChange("companyName", e.target.value)
                }
                className={errors.companyName ? "error" : ""}
                placeholder="Enter company name"
              />
              {errors.companyName && (
                <span className="error-message">{errors.companyName}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="contactPerson">Contact Person *</label>
              <input
                type="text"
                id="contactPerson"
                value={companyInfo.contactPerson}
                onChange={(e) =>
                  handleInputChange("contactPerson", e.target.value)
                }
                className={errors.contactPerson ? "error" : ""}
                placeholder="Enter contact person name"
              />
              {errors.contactPerson && (
                <span className="error-message">{errors.contactPerson}</span>
              )}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone">Phone Number *</label>
              <input
                type="tel"
                id="phone"
                value={companyInfo.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className={errors.phone ? "error" : ""}
                placeholder="Enter phone number"
              />
              {errors.phone && (
                <span className="error-message">{errors.phone}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="email">Email Address *</label>
              <input
                type="email"
                id="email"
                value={companyInfo.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={errors.email ? "error" : ""}
                placeholder="Enter email address"
              />
              {errors.email && (
                <span className="error-message">{errors.email}</span>
              )}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="panNumber">PAN Number</label>
              <input
                type="text"
                id="panNumber"
                value={companyInfo.panNumber}
                onChange={(e) =>
                  handleInputChange("panNumber", e.target.value.toUpperCase())
                }
                placeholder="Enter PAN number"
                maxLength={10}
              />
            </div>

            <div className="form-group">
              <label htmlFor="gstNumber">GST Number</label>
              <input
                type="text"
                id="gstNumber"
                value={companyInfo.gstNumber}
                onChange={(e) =>
                  handleInputChange("gstNumber", e.target.value.toUpperCase())
                }
                placeholder="Enter GST number"
                maxLength={15}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="numberOfYears">
                Number of Years for Analysis *
              </label>
              <select
                id="numberOfYears"
                value={companyInfo.numberOfYears}
                onChange={(e) =>
                  handleInputChange("numberOfYears", parseInt(e.target.value))
                }
                className="form-select"
              >
                <option value={2}>2 Years</option>
                <option value={3}>3 Years (Recommended)</option>
                <option value={4}>4 Years</option>
                <option value={5}>5 Years</option>
              </select>
              <small className="form-help-text">
                Select how many years of financial data you want to analyze. 3
                years is recommended for comprehensive analysis.
              </small>
            </div>

            <div className="form-group">
              {/* Empty div to maintain grid layout */}
            </div>
          </div>

          <div className="form-group full-width">
            <label htmlFor="address">Address</label>
            <textarea
              id="address"
              value={companyInfo.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              placeholder="Enter complete address"
              rows={3}
            />
          </div>

          <div className="form-actions">
            <button type="submit" className="btn-primary">
              Continue to Balance Sheet Entry
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CompanySetup;
