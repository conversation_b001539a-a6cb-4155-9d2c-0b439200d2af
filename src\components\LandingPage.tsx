import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  CheckCircle,
  Star,
  TrendingUp,
  Shield,
  Zap,
  Users,
  Award,
  FileText,
  BarChart3,
  Download,
  Clock,
} from "lucide-react";
import "./LandingPage.css";

interface Props {
  onGetStarted: () => void;
  onLogin: () => void;
  onPricing: () => void;
  onContact: () => void;
}

const LandingPage: React.FC<Props> = ({
  onGetStarted,
  onLogin,
  onPricing,
  onContact,
}) => {
  return (
    <div className="landing-page">
      {/* Header */}
      <header className="landing-header">
        <div className="container">
          <div className="header-content">
            <div className="logo">
              <BarChart3 className="logo-icon" />
              <span className="logo-text">Zheet.io</span>
            </div>
            <nav className="nav-menu">
              <a href="#features">Features</a>
              <button onClick={onPricing} className="nav-link">
                Pricing
              </button>
              <a href="#testimonials">Reviews</a>
              <button onClick={onContact} className="nav-link">
                Contact
              </button>
            </nav>
            <div className="header-actions">
              <button onClick={onLogin} className="btn-login">
                Login
              </button>
              <button onClick={onGetStarted} className="btn-primary">
                Get Started Free
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">
                AI-Powered Balance Sheet Analysis
                <span className="highlight"> Made Simple</span>
              </h1>
              <p className="hero-description">
                Transform your financial documents into actionable insights with
                90%+ accuracy. Upload PDFs, get instant analysis, and make
                data-driven decisions faster than ever.
              </p>

              <div className="hero-features">
                <div className="feature-item">
                  <CheckCircle className="feature-icon" />
                  <span>Upload PDF/Images</span>
                </div>
                <div className="feature-item">
                  <CheckCircle className="feature-icon" />
                  <span>AI Data Extraction</span>
                </div>
                <div className="feature-item">
                  <CheckCircle className="feature-icon" />
                  <span>Instant Analysis</span>
                </div>
                <div className="feature-item">
                  <CheckCircle className="feature-icon" />
                  <span>Excel Export</span>
                </div>
              </div>
              <div className="hero-actions">
                <button onClick={onGetStarted} className="btn-hero-primary">
                  Start Free Trial <ArrowRight className="btn-icon" />
                </button>
                <button className="btn-hero-secondary">Watch Demo</button>
              </div>
              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">10,000+</span>
                  <span className="stat-label">Documents Analyzed</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">90%+</span>
                  <span className="stat-label">Accuracy Rate</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">5 Min</span>
                  <span className="stat-label">Average Processing</span>
                </div>
              </div>
            </div>
            <div className="hero-right">
              {/* Create Balance Sheet Banner - Moved to top */}
              <div className="right-create-banner">
                <div className="right-banner-content">
                  <div className="right-banner-icon">
                    <FileText className="right-banner-icon-svg" />
                  </div>
                  <div className="right-banner-text">
                    <h3 className="right-banner-title">
                      Create Professional Balance Sheets
                    </h3>
                    <p className="right-banner-description">
                      Generate professional balance sheets from your assets and
                      liabilities with our AI-powered tool
                    </p>
                  </div>
                  <button onClick={onGetStarted} className="right-banner-cta">
                    Create Balance Sheet
                    <ArrowRight className="right-banner-arrow" />
                  </button>
                </div>
              </div>

              {/* Dashboard Preview - Moved to bottom */}
              <div className="hero-visual">
                <div className="dashboard-preview">
                  <div className="preview-header">
                    <div className="preview-tabs">
                      <span className="tab active">Balance Sheet</span>
                      <span className="tab">P&L Analysis</span>
                      <span className="tab">Ratios</span>
                    </div>
                  </div>
                  <div className="preview-content">
                    <div className="chart-placeholder">
                      <TrendingUp size={48} className="chart-icon" />
                      <div className="chart-bars">
                        <div className="bar" style={{ height: "60%" }}></div>
                        <div className="bar" style={{ height: "80%" }}></div>
                        <div className="bar" style={{ height: "45%" }}></div>
                        <div className="bar" style={{ height: "90%" }}></div>
                        <div className="bar" style={{ height: "70%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features-section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">
              Powerful Features for Financial Analysis
            </h2>
            <p className="section-description">
              Everything you need to analyze balance sheets and make informed
              financial decisions
            </p>
          </div>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <FileText className="feature-icon" />
              </div>
              <h3 className="feature-title">Smart Document Upload</h3>
              <p className="feature-description">
                Upload balance sheets in PDF or image format. Our AI
                automatically extracts and organizes financial data.
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <Zap className="feature-icon" />
              </div>
              <h3 className="feature-title">Instant AI Analysis</h3>
              <p className="feature-description">
                Get comprehensive financial analysis in seconds with 90%+
                accuracy using advanced AI algorithms.
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <BarChart3 className="feature-icon" />
              </div>
              <h3 className="feature-title">Advanced Ratios</h3>
              <p className="feature-description">
                Calculate key financial ratios, working capital, and performance
                metrics automatically.
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <Download className="feature-icon" />
              </div>
              <h3 className="feature-title">Excel Export</h3>
              <p className="feature-description">
                Export your analysis to professional Excel reports for
                presentations and further analysis.
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <Shield className="feature-icon" />
              </div>
              <h3 className="feature-title">Bank-Grade Security</h3>
              <p className="feature-description">
                Your financial data is protected with enterprise-level security
                and encryption.
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon-wrapper">
                <Clock className="feature-icon" />
              </div>
              <h3 className="feature-title">Multi-Year Analysis</h3>
              <p className="feature-description">
                Compare performance across multiple years and identify trends
                and patterns.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="social-proof-section">
        <div className="container">
          <div className="social-proof-content">
            <div className="proof-stats">
              <div className="proof-item">
                <Users className="proof-icon" />
                <span className="proof-number">2,500+</span>
                <span className="proof-label">Happy Customers</span>
              </div>
              <div className="proof-item">
                <Award className="proof-icon" />
                <span className="proof-number">4.9/5</span>
                <span className="proof-label">Customer Rating</span>
              </div>
              <div className="proof-item">
                <TrendingUp className="proof-icon" />
                <span className="proof-number">99.9%</span>
                <span className="proof-label">Uptime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="testimonials-section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">What Our Customers Say</h2>
          </div>
          <div className="testimonials-grid">
            <div className="testimonial-card">
              <div className="testimonial-rating">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="star-icon filled" />
                ))}
              </div>
              <p className="testimonial-text">
                "FinanceAI Pro has revolutionized our financial analysis
                process. What used to take hours now takes minutes!"
              </p>
              <div className="testimonial-author">
                <div className="author-info">
                  <span className="author-name">Sarah Johnson</span>
                  <span className="author-title">CFO, TechCorp</span>
                </div>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="testimonial-rating">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="star-icon filled" />
                ))}
              </div>
              <p className="testimonial-text">
                "The accuracy is incredible. It's like having a financial
                analyst available 24/7."
              </p>
              <div className="testimonial-author">
                <div className="author-info">
                  <span className="author-name">Michael Chen</span>
                  <span className="author-title">Financial Advisor</span>
                </div>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="testimonial-rating">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="star-icon filled" />
                ))}
              </div>
              <p className="testimonial-text">
                "The lifetime deal was a no-brainer. Best investment we've made
                for our accounting firm."
              </p>
              <div className="testimonial-author">
                <div className="author-info">
                  <span className="author-name">Emily Rodriguez</span>
                  <span className="author-title">
                    Managing Partner, Rodriguez & Associates
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2 className="cta-title">
              Ready to Transform Your Financial Analysis?
            </h2>
            <p className="cta-description">
              Join thousands of professionals who trust Zheet.io for accurate,
              fast financial insights.
            </p>
            <div className="cta-actions">
              <button onClick={onGetStarted} className="btn-cta-primary">
                Start Free Trial <ArrowRight className="btn-icon" />
              </button>
              <p className="cta-note">
                No credit card required • 1-day free trial
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <div className="logo">
                <BarChart3 className="logo-icon" />
                <span className="logo-text">Zheet.io</span>
              </div>
              <p className="footer-description">
                AI-powered financial analysis platform trusted by professionals
                worldwide.
              </p>
            </div>
            <div className="footer-links">
              <div className="link-group">
                <h4>Product</h4>
                <a href="#features">Features</a>
                <a href="#pricing">Pricing</a>
                <a href="#api">API</a>
              </div>
              <div className="link-group">
                <h4>Support</h4>
                <a href="#help">Help Center</a>
                <a href="#contact">Contact</a>
                <a href="#status">Status</a>
              </div>
              <div className="link-group">
                <h4>Legal</h4>
                <a href="#privacy">Privacy</a>
                <a href="#terms">Terms</a>
                <a href="#security">Security</a>
              </div>
            </div>
          </div>
          <div className="footer-bottom">
            <p>
              &copy; 2024 S.Vivekrajiv. All rights reserved. | Full Stack Web
              Developer & Freelancer
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
