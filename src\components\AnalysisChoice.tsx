import React from "react";
import { CompanyInfo, AnalysisChoice } from "../types/FinancialData";
import { ArrowLeft, FileText, Upload, BarChart3 } from "lucide-react";
import "./AnalysisChoice.css";

interface Props {
  company: CompanyInfo;
  onChoiceSelect: (choice: AnalysisChoice) => void;
  onBack?: () => void;
  showBackButton?: boolean;
}

const AnalysisChoiceComponent: React.FC<Props> = ({
  company,
  onChoiceSelect,
  onBack,
  showBackButton = false,
}) => {
  return (
    <div className="analysis-choice">
      <div className="choice-container">
        <div className="choice-header">
          {showBackButton && onBack && (
            <button onClick={onBack} className="back-to-home">
              <ArrowLeft className="back-icon" />
              Back to Home
            </button>
          )}
          <h1>Balance Sheet Analysis</h1>
          <h2>{company.companyName}</h2>
          <p>
            Choose how you'd like to enter your balance sheet data for analysis
          </p>
        </div>

        <div className="choice-options analysis-options">
          <div
            className="choice-card analyze-card"
            onClick={() => onChoiceSelect("analyze-existing")}
          >
            <div className="choice-icon-wrapper manual">
              <FileText className="choice-icon" />
            </div>
            <h3>Manual Balance Sheet Data Entry</h3>
            <p>
              Enter your balance sheet data manually using our step-by-step
              forms for comprehensive financial analysis
            </p>
            <ul>
              <li>Guided data entry forms</li>
              <li>
                Multi-year data collection ({company.numberOfYears} years)
              </li>
              <li>Real-time validation and calculations</li>
              <li>P&L Analysis and Ratio Analysis</li>
              <li>Working capital assessment</li>
            </ul>
            <div className="choice-button">
              <span>Start Manual Entry</span>
              <BarChart3 className="button-icon" />
            </div>
          </div>

          <div
            className="choice-card upload-card"
            onClick={() => onChoiceSelect("upload-analysis")}
          >
            <div className="choice-icon-wrapper upload">
              <Upload className="choice-icon" />
            </div>
            <h3>Upload Balance Sheet Documents</h3>
            <p>
              Upload your balance sheet PDF files or images and let our AI
              extract the financial data automatically
            </p>
            <ul>
              <li>Upload PDF or image files</li>
              <li>AI-powered data extraction (90%+ accuracy)</li>
              <li>Automatic form population</li>
              <li>Support for {company.numberOfYears} years of documents</li>
              <li>Review and edit extracted data</li>
            </ul>
            <div className="choice-button">
              <span>Upload & Analyze</span>
              <Upload className="button-icon" />
            </div>
          </div>
        </div>

        <div className="choice-footer">
          <p>
            <strong>Need help deciding?</strong>
            <br />
            Choose "Manual Entry" for step-by-step guided data input with
            real-time validation.
            <br />
            Choose "Upload" to automatically extract data from your existing
            balance sheet documents.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AnalysisChoiceComponent;
