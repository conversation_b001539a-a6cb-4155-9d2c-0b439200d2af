import React from "react";
import {
  BarChart3,
  FileText,
  Upload,
  PlusCircle,
  TrendingUp,
  Users,
  Settings,
  LogOut,
  User as UserIcon,
  ChevronRight,
  Activity,
  DollarSign,
} from "lucide-react";
import { User, CompanyInfo } from "../types/FinancialData";
import {
  getUserDisplayName,
  getSubscriptionDisplayInfo,
} from "../utils/testUser";
import { getSessionInfo, formatTimeRemaining } from "../utils/sessionManager";
import "./UserHomePage.css";

interface Props {
  user: User;
  company: CompanyInfo | null;
  onAnalyzeBalanceSheet: () => void;
  onCreateBalanceSheet: () => void;
  onLogout: () => void;
  onSettings: () => void;
  onNewCompany?: () => void;
  onLaunchOffer?: () => void;
}

interface NavigationState {
  activeTab: string;
}

const UserHomePage: React.FC<Props> = ({
  user,
  company,
  onAnalyzeBalanceSheet,
  onCreateBalanceSheet,
  onLogout,
  onSettings,
  onNewCompany,
  onLaunchOffer,
}) => {
  const [activeTab, setActiveTab] = React.useState<string>("dashboard");
  const subscriptionInfo = getSubscriptionDisplayInfo(user);
  const sessionInfo = getSessionInfo();

  const handleNavClick = (tab: string) => {
    setActiveTab(tab);
    // For now, just show an alert. In a real app, you'd navigate to different views
    switch (tab) {
      case "dashboard":
        // Already on dashboard
        break;
      case "analytics":
        alert("Analytics feature coming soon!");
        break;
      case "reports":
        alert("Reports feature coming soon!");
        break;
      case "help":
        alert("Help & Support feature coming soon!");
        break;
    }
  };

  return (
    <div className="user-home-page">
      {/* Navigation Header */}
      <header className="home-header">
        <div className="header-container">
          <div className="brand-section">
            <div className="brand-logo">
              <BarChart3 className="brand-icon" />
              <span className="brand-name">FinanceAI Pro</span>
            </div>
          </div>

          <nav className="main-nav">
            <button
              onClick={() => handleNavClick("dashboard")}
              className={`nav-link ${
                activeTab === "dashboard" ? "active" : ""
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => handleNavClick("analytics")}
              className={`nav-link ${
                activeTab === "analytics" ? "active" : ""
              }`}
            >
              Analytics
            </button>
            <button
              onClick={() => handleNavClick("reports")}
              className={`nav-link ${activeTab === "reports" ? "active" : ""}`}
            >
              Reports
            </button>
            <button
              onClick={() => handleNavClick("help")}
              className={`nav-link ${activeTab === "help" ? "active" : ""}`}
            >
              Help
            </button>
          </nav>

          <div className="user-section">
            <div className="user-info">
              <div className="user-avatar">
                <UserIcon className="avatar-icon" />
              </div>
              <div className="user-details">
                <span className="user-name">{getUserDisplayName(user)}</span>
                <span className="user-plan">{subscriptionInfo.planName}</span>
                {sessionInfo.isActive && (
                  <span className="session-info">
                    Session: {formatTimeRemaining(sessionInfo.timeRemaining)}
                  </span>
                )}
              </div>
            </div>
            <div className="user-actions">
              <button
                onClick={onSettings}
                className="action-btn"
                title="Settings"
              >
                <Settings className="action-icon" />
              </button>
              <button
                onClick={onLogout}
                className="action-btn logout"
                title="Logout"
              >
                <LogOut className="action-icon" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Launch Offer Banner */}
      <div className="launch-offer-banner">
        <div className="offer-container">
          <div className="offer-content">
            <div className="offer-badge">🚀 LAUNCH OFFER</div>
            <div className="offer-text">
              <span className="offer-title">Lifetime Access for $100</span>
              <span className="offer-subtitle">
                Limited Time • Save 90% • No Monthly Fees
              </span>
            </div>
            <div className="offer-action">
              <button className="offer-btn">
                Claim Offer
                <ChevronRight className="offer-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Create Balance Sheet Banner */}
      <div className="create-balance-banner">
        <div className="create-banner-container">
          <div className="create-banner-content">
            <div className="create-banner-left">
              <div className="create-banner-icon">
                <FileText className="create-banner-icon-svg" />
              </div>
              <div className="create-banner-text">
                <h3 className="create-banner-title">
                  Create Professional Balance Sheets
                </h3>
                <p className="create-banner-description">
                  Generate professional balance sheets from your assets and
                  liabilities with AI-powered tools
                </p>
              </div>
            </div>
            <button
              onClick={onCreateBalanceSheet}
              className="create-banner-cta"
            >
              Create Balance Sheet
              <ChevronRight className="create-banner-arrow" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="home-main">
        <div className="main-container">
          {/* Welcome Section */}
          <section className="welcome-section">
            <div className="welcome-content">
              <h1 className="welcome-title">
                Welcome back, {user.firstName}! 👋
              </h1>
              <p className="welcome-subtitle">
                {company
                  ? `Continue working with ${company.companyName}`
                  : "Ready to analyze your financial data?"}
              </p>
            </div>

            {company && (
              <div className="company-card">
                <div className="company-info">
                  <h3 className="company-name">{company.companyName}</h3>
                  <p className="company-details">{company.contactPerson}</p>
                  <p className="company-analysis">
                    Analysis Period: {company.numberOfYears} Years
                  </p>
                </div>
                <div className="company-stats">
                  <div className="stat-item">
                    <Activity className="stat-icon" />
                    <span className="stat-label">Active</span>
                  </div>
                  {onNewCompany && (
                    <button
                      onClick={onNewCompany}
                      className="new-company-btn"
                      title="Setup New Company"
                    >
                      <PlusCircle className="new-company-icon" />
                      New Company
                    </button>
                  )}
                </div>
              </div>
            )}
          </section>

          {/* Quick Actions */}
          <section className="quick-actions">
            <h2 className="section-title">What would you like to do?</h2>

            <div className="actions-grid">
              {/* Analyze Balance Sheet */}
              <div
                className="action-card primary"
                onClick={onAnalyzeBalanceSheet}
              >
                <div className="card-header">
                  <div className="card-icon-wrapper analyze">
                    <BarChart3 className="card-icon" />
                  </div>
                  <h3 className="card-title">Analyze Balance Sheet</h3>
                </div>
                <p className="card-description">
                  Upload or manually enter your balance sheet data for
                  comprehensive financial analysis
                </p>
                <div className="card-features">
                  <span className="feature-tag">• Multi-year Analysis</span>
                  <span className="feature-tag">• AI-Powered Insights</span>
                  <span className="feature-tag">• Ratio Analysis</span>
                </div>
                <div className="card-action">
                  <span className="action-text">Start Analysis</span>
                  <ChevronRight className="action-arrow" />
                </div>
              </div>

              {/* Create Balance Sheet */}
              <div
                className="action-card secondary"
                onClick={onCreateBalanceSheet}
              >
                <div className="card-header">
                  <div className="card-icon-wrapper create">
                    <PlusCircle className="card-icon" />
                  </div>
                  <h3 className="card-title">Create Balance Sheet</h3>
                </div>
                <p className="card-description">
                  Generate a professional balance sheet from your total assets
                  and liabilities
                </p>
                <div className="card-features">
                  <span className="feature-tag">• Auto-Generated</span>
                  <span className="feature-tag">• PDF Export</span>
                  <span className="feature-tag">• Excel Format</span>
                </div>
                <div className="card-action">
                  <span className="action-text">Create New</span>
                  <ChevronRight className="action-arrow" />
                </div>
              </div>
            </div>
          </section>

          {/* Recent Activity */}
          <section className="recent-activity">
            <h2 className="section-title">Recent Activity</h2>
            <div className="activity-card">
              <div className="activity-content">
                <div className="activity-icon-wrapper">
                  <TrendingUp className="activity-icon" />
                </div>
                <div className="activity-details">
                  <h4 className="activity-title">Ready to get started</h4>
                  <p className="activity-description">
                    Begin your financial analysis journey with our AI-powered
                    tools
                  </p>
                  <span className="activity-time">Just now</span>
                </div>
              </div>
            </div>
          </section>

          {/* Features Overview */}
          <section className="features-overview">
            <h2 className="section-title">Platform Features</h2>
            <div className="features-grid">
              <div className="feature-item">
                <Upload className="feature-icon" />
                <h4 className="feature-title">Smart Upload</h4>
                <p className="feature-desc">
                  AI-powered data extraction from PDFs and images
                </p>
              </div>
              <div className="feature-item">
                <FileText className="feature-icon" />
                <h4 className="feature-title">Professional Reports</h4>
                <p className="feature-desc">
                  Generate comprehensive financial reports
                </p>
              </div>
              <div className="feature-item">
                <DollarSign className="feature-icon" />
                <h4 className="feature-title">Financial Ratios</h4>
                <p className="feature-desc">
                  Advanced ratio analysis and insights
                </p>
              </div>
              <div className="feature-item">
                <Users className="feature-icon" />
                <h4 className="feature-title">Multi-Company</h4>
                <p className="feature-desc">Manage multiple company profiles</p>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  );
};

export default UserHomePage;
