import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database Types (generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          role: 'user' | 'manager' | 'super_admin'
          subscription_type: 'free' | 'premium' | 'lifetime'
          subscription_status: 'active' | 'expired' | 'cancelled'
          subscription_start_date: string
          subscription_end_date: string | null
          usage_count: number
          max_usage: number
          is_email_verified: boolean
          created_at: string
          last_login_at: string | null
          profile_image: string | null
          company: string | null
          phone: string | null
        }
        Insert: {
          id?: string
          email: string
          first_name: string
          last_name: string
          role?: 'user' | 'manager' | 'super_admin'
          subscription_type?: 'free' | 'premium' | 'lifetime'
          subscription_status?: 'active' | 'expired' | 'cancelled'
          subscription_start_date?: string
          subscription_end_date?: string | null
          usage_count?: number
          max_usage?: number
          is_email_verified?: boolean
          created_at?: string
          last_login_at?: string | null
          profile_image?: string | null
          company?: string | null
          phone?: string | null
        }
        Update: {
          id?: string
          email?: string
          first_name?: string
          last_name?: string
          role?: 'user' | 'manager' | 'super_admin'
          subscription_type?: 'free' | 'premium' | 'lifetime'
          subscription_status?: 'active' | 'expired' | 'cancelled'
          subscription_start_date?: string
          subscription_end_date?: string | null
          usage_count?: number
          max_usage?: number
          is_email_verified?: boolean
          created_at?: string
          last_login_at?: string | null
          profile_image?: string | null
          company?: string | null
          phone?: string | null
        }
      }
      companies: {
        Row: {
          id: string
          user_id: string
          company_name: string
          address: string
          contact_person: string
          phone: string
          email: string
          pan_number: string
          gst_number: string
          number_of_years: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          company_name: string
          address: string
          contact_person: string
          phone: string
          email: string
          pan_number: string
          gst_number: string
          number_of_years: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          company_name?: string
          address?: string
          contact_person?: string
          phone?: string
          email?: string
          pan_number?: string
          gst_number?: string
          number_of_years?: number
          created_at?: string
          updated_at?: string
        }
      }
      balance_sheet_analyses: {
        Row: {
          id: string
          user_id: string
          company_id: string
          analysis_name: string
          analysis_type: 'manual' | 'upload' | 'generated'
          status: 'draft' | 'completed' | 'archived'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          company_id: string
          analysis_name: string
          analysis_type: 'manual' | 'upload' | 'generated'
          status?: 'draft' | 'completed' | 'archived'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          company_id?: string
          analysis_name?: string
          analysis_type?: 'manual' | 'upload' | 'generated'
          status?: 'draft' | 'completed' | 'archived'
          created_at?: string
          updated_at?: string
        }
      }
      balance_sheet_data: {
        Row: {
          id: string
          analysis_id: string
          year: string
          year_type: 'Previous Year (Audited)' | 'Current Year (Actual)' | 'Projected Next Year'
          liabilities_data: any // JSON
          assets_data: any // JSON
          profit_loss_data: any // JSON
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          year: string
          year_type: 'Previous Year (Audited)' | 'Current Year (Actual)' | 'Projected Next Year'
          liabilities_data: any
          assets_data: any
          profit_loss_data: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          year?: string
          year_type?: 'Previous Year (Audited)' | 'Current Year (Actual)' | 'Projected Next Year'
          liabilities_data?: any
          assets_data?: any
          profit_loss_data?: any
          created_at?: string
          updated_at?: string
        }
      }
      uploaded_files: {
        Row: {
          id: string
          user_id: string
          analysis_id: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          extraction_status: 'pending' | 'processing' | 'completed' | 'error'
          extracted_data: any | null // JSON
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          analysis_id: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          extraction_status?: 'pending' | 'processing' | 'completed' | 'error'
          extracted_data?: any | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          analysis_id?: string
          file_name?: string
          file_path?: string
          file_size?: number
          file_type?: string
          extraction_status?: 'pending' | 'processing' | 'completed' | 'error'
          extracted_data?: any | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
