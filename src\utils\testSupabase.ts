import { supabase } from '../lib/supabase'

// Test Supabase connection and basic functionality
export const testSupabaseConnection = async () => {
  console.log('🧪 Testing Supabase Connection...')
  
  try {
    // Test 1: Basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message)
      return false
    }
    
    console.log('✅ Supabase connection successful')
    
    // Test 2: Check if tables exist
    const tables = ['users', 'companies', 'balance_sheet_analyses', 'balance_sheet_data']
    
    for (const table of tables) {
      const { error: tableError } = await supabase
        .from(table)
        .select('count(*)')
        .limit(1)
      
      if (tableError) {
        console.error(`❌ Table '${table}' not found:`, tableError.message)
        return false
      }
      
      console.log(`✅ Table '${table}' exists`)
    }
    
    // Test 3: Check storage bucket
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets()
    
    if (bucketError) {
      console.error('❌ Storage bucket check failed:', bucketError.message)
      return false
    }
    
    const uploadBucket = buckets?.find(bucket => bucket.name === 'balance-sheet-uploads')
    if (uploadBucket) {
      console.log('✅ Storage bucket "balance-sheet-uploads" exists')
    } else {
      console.warn('⚠️ Storage bucket "balance-sheet-uploads" not found')
    }
    
    // Test 4: Check test user exists
    const { data: testUser, error: testUserError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()
    
    if (testUserError) {
      console.warn('⚠️ Test user not found. You may need to create it manually.')
    } else {
      console.log('✅ Test user exists:', testUser.email)
    }
    
    console.log('🎉 All Supabase tests passed!')
    return true
    
  } catch (error) {
    console.error('❌ Unexpected error during Supabase testing:', error)
    return false
  }
}

// Test authentication flow
export const testAuthFlow = async () => {
  console.log('🔐 Testing Authentication Flow...')
  
  try {
    // Test sign up with a temporary user
    const testEmail = `test-${Date.now()}@example.com`
    const testPassword = 'TestPassword123!'
    
    console.log(`📝 Testing sign up with: ${testEmail}`)
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
          company: 'Test Company',
          phone: '1234567890'
        }
      }
    })
    
    if (signUpError) {
      console.error('❌ Sign up failed:', signUpError.message)
      return false
    }
    
    console.log('✅ Sign up successful')
    
    // Clean up - remove test user
    if (signUpData.user) {
      await supabase.auth.admin.deleteUser(signUpData.user.id)
      console.log('🧹 Test user cleaned up')
    }
    
    return true
    
  } catch (error) {
    console.error('❌ Auth flow test failed:', error)
    return false
  }
}

// Run all tests
export const runAllTests = async () => {
  console.log('🚀 Starting Supabase Backend Tests...')
  console.log('=====================================')
  
  const connectionTest = await testSupabaseConnection()
  
  if (connectionTest) {
    console.log('=====================================')
    await testAuthFlow()
  }
  
  console.log('=====================================')
  console.log('🏁 Backend testing complete!')
  
  return connectionTest
}

// Add to window for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testSupabase = {
    testConnection: testSupabaseConnection,
    testAuth: testAuthFlow,
    runAllTests
  }
  
  console.log('🔧 Supabase test functions available:')
  console.log('- window.testSupabase.testConnection()')
  console.log('- window.testSupabase.testAuth()')
  console.log('- window.testSupabase.runAllTests()')
}
