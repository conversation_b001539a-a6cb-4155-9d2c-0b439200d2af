import React from "react";
import { BalanceSheetData, YearlyData } from "../types/FinancialData";
import "./BalanceSheetForm.css";

interface Props {
  data: BalanceSheetData;
  onChange: (data: BalanceSheetData) => void;
}

const BalanceSheetForm: React.FC<Props> = ({ data, onChange }) => {
  const updateField = (
    section: keyof BalanceSheetData,
    field: string,
    year: keyof YearlyData,
    value: number
  ) => {
    const newData = { ...data };
    (newData[section] as any)[field][year] = value;
    onChange(newData);
  };

  const renderYearlyInputs = (
    section: keyof BalanceSheetData,
    field: string,
    label: string,
    yearlyData: YearlyData
  ) => (
    <tr key={field}>
      <td className="field-label">{label}</td>
      <td>
        <input
          type="number"
          value={yearlyData.previousYear || ""}
          onChange={(e) =>
            updateField(
              section,
              field,
              "previousYear",
              parseFloat(e.target.value) || 0
            )
          }
          placeholder="0"
        />
      </td>
      <td>
        <input
          type="number"
          value={yearlyData.currentYear || ""}
          onChange={(e) =>
            updateField(
              section,
              field,
              "currentYear",
              parseFloat(e.target.value) || 0
            )
          }
          placeholder="0"
        />
      </td>
      <td>
        <input
          type="number"
          value={yearlyData.projectedYear || ""}
          onChange={(e) =>
            updateField(
              section,
              field,
              "projectedYear",
              parseFloat(e.target.value) || 0
            )
          }
          placeholder="0"
        />
      </td>
    </tr>
  );

  const calculateSectionTotal = (sectionData: any): YearlyData => {
    const total: YearlyData = {
      previousYear: 0,
      currentYear: 0,
      projectedYear: 0,
    };
    Object.values(sectionData).forEach((fieldData: any) => {
      total.previousYear += fieldData.previousYear || 0;
      total.currentYear += fieldData.currentYear || 0;
      total.projectedYear += fieldData.projectedYear || 0;
    });
    return total;
  };

  const renderSectionTotal = (label: string, total: YearlyData) => (
    <tr className="section-total">
      <td className="field-label">
        <strong>{label}</strong>
      </td>
      <td>
        <strong>{total.previousYear.toLocaleString()}</strong>
      </td>
      <td>
        <strong>{total.currentYear.toLocaleString()}</strong>
      </td>
      <td>
        <strong>{total.projectedYear.toLocaleString()}</strong>
      </td>
    </tr>
  );

  return (
    <div className="balance-sheet-form">
      <h2>Balance Sheet Data Entry</h2>

      <table className="financial-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Previous Year (Audited)</th>
            <th>Current Year (Actual)</th>
            <th>Projected Next Year</th>
          </tr>
        </thead>
        <tbody>
          {/* Current Liabilities Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>A. Current Liabilities</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "currentLiabilities",
            "tradeCreditors",
            "Trade Creditors",
            data.currentLiabilities.tradeCreditors
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "expensesDutyOtherPayables",
            "Expenses / Duty / Other payables",
            data.currentLiabilities.expensesDutyOtherPayables
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "lcCreditorsJobWork",
            "LC creditors / Creditors for job work",
            data.currentLiabilities.lcCreditorsJobWork
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "advanceReceivedFromBuyers",
            "Advance received from buyers",
            data.currentLiabilities.advanceReceivedFromBuyers
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "shortTermBankFinance",
            "Short term bank finance",
            data.currentLiabilities.shortTermBankFinance
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "provisions",
            "Provisions",
            data.currentLiabilities.provisions
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "taxPayable",
            "Tax Payable",
            data.currentLiabilities.taxPayable
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "commercialPaper",
            "Commercial Paper",
            data.currentLiabilities.commercialPaper
          )}
          {renderYearlyInputs(
            "currentLiabilities",
            "sdMaturityRepayableOneYear",
            "SD Maturity repayable one year",
            data.currentLiabilities.sdMaturityRepayableOneYear
          )}
          {renderSectionTotal(
            "Total Current Liabilities (A)",
            calculateSectionTotal(data.currentLiabilities)
          )}

          {/* Branch / Sister Concerns Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>B. Branch / Sister Concerns A/c</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "branchSisterConcerns",
            "havingCreditFacilities",
            "Having credit facilities with us",
            data.branchSisterConcerns.havingCreditFacilities
          )}
          {renderYearlyInputs(
            "branchSisterConcerns",
            "notHavingFacilities",
            "Not having facilities with us",
            data.branchSisterConcerns.notHavingFacilities
          )}
          {renderSectionTotal(
            "Total of (B)",
            calculateSectionTotal(data.branchSisterConcerns)
          )}

          {/* Long Term Liabilities Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>C. Long Term Liabilities</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "longTermLiabilities",
            "longTermFundsFromFamily",
            "Long Term Funds from Family Members",
            data.longTermLiabilities.longTermFundsFromFamily
          )}
          {renderYearlyInputs(
            "longTermLiabilities",
            "longTermLoanFromBank",
            "Long Term Loan from our bank",
            data.longTermLiabilities.longTermLoanFromBank
          )}
          {renderYearlyInputs(
            "longTermLiabilities",
            "longTermLoanFromInstitutions",
            "Long Term Loan from Financial Institutions",
            data.longTermLiabilities.longTermLoanFromInstitutions
          )}
          {renderYearlyInputs(
            "longTermLiabilities",
            "cautionDeposit",
            "Caution deposit",
            data.longTermLiabilities.cautionDeposit
          )}
          {renderSectionTotal(
            "Total Long Term Liabilities (C)",
            calculateSectionTotal(data.longTermLiabilities)
          )}

          {/* Capital and Reserve Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>D. Capital and Reserve</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "capitalAndReserve",
            "paidUpCapital",
            "Paid up Capital",
            data.capitalAndReserve.paidUpCapital
          )}
          {renderYearlyInputs(
            "capitalAndReserve",
            "reservesAndSurplus",
            "Reserves and surplus",
            data.capitalAndReserve.reservesAndSurplus
          )}
          {renderYearlyInputs(
            "capitalAndReserve",
            "convertiblePreferenceShare",
            "Convertible Preference Share",
            data.capitalAndReserve.convertiblePreferenceShare
          )}
          {renderYearlyInputs(
            "capitalAndReserve",
            "plAccount",
            "P & L account",
            data.capitalAndReserve.plAccount
          )}
          {renderYearlyInputs(
            "capitalAndReserve",
            "lessMiscExpensesDrawings",
            "Less: Misc. Expenses / Drawings",
            data.capitalAndReserve.lessMiscExpensesDrawings
          )}
          {renderSectionTotal(
            "Net Capital and Reserves (D)",
            calculateSectionTotal(data.capitalAndReserve)
          )}

          {/* Total Liabilities */}
          {(() => {
            const totalLiabilities: YearlyData = {
              previousYear:
                calculateSectionTotal(data.currentLiabilities).previousYear +
                calculateSectionTotal(data.branchSisterConcerns).previousYear +
                calculateSectionTotal(data.longTermLiabilities).previousYear +
                calculateSectionTotal(data.capitalAndReserve).previousYear,
              currentYear:
                calculateSectionTotal(data.currentLiabilities).currentYear +
                calculateSectionTotal(data.branchSisterConcerns).currentYear +
                calculateSectionTotal(data.longTermLiabilities).currentYear +
                calculateSectionTotal(data.capitalAndReserve).currentYear,
              projectedYear:
                calculateSectionTotal(data.currentLiabilities).projectedYear +
                calculateSectionTotal(data.branchSisterConcerns).projectedYear +
                calculateSectionTotal(data.longTermLiabilities).projectedYear +
                calculateSectionTotal(data.capitalAndReserve).projectedYear,
            };
            return renderSectionTotal(
              "Total of Liabilities (A+B+C+D)",
              totalLiabilities
            );
          })()}

          {/* Current Assets Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>E. Current Assets</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "currentAssets",
            "cashAndBankBalance",
            "Cash and Bank Balance",
            data.currentAssets.cashAndBankBalance
          )}
          {renderYearlyInputs(
            "currentAssets",
            "stock",
            "Stock",
            data.currentAssets.stock
          )}
          {renderYearlyInputs(
            "currentAssets",
            "receivables",
            "Receivables",
            data.currentAssets.receivables
          )}
          {renderYearlyInputs(
            "currentAssets",
            "shortTermAdvance",
            "Short term advance",
            data.currentAssets.shortTermAdvance
          )}
          {renderYearlyInputs(
            "currentAssets",
            "otherCurrentAsset",
            "Other current asset",
            data.currentAssets.otherCurrentAsset
          )}
          {renderYearlyInputs(
            "currentAssets",
            "depositAdvances",
            "Deposit / Advances",
            data.currentAssets.depositAdvances
          )}
          {renderYearlyInputs(
            "currentAssets",
            "others",
            "Others (Viz. Pre paid expenses etc)",
            data.currentAssets.others
          )}
          {renderSectionTotal(
            "Total Current Assets (E)",
            calculateSectionTotal(data.currentAssets)
          )}

          {/* Fixed Assets Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>F. Fixed Assets</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "fixedAssets",
            "landAndBuilding",
            "Land and Building",
            data.fixedAssets.landAndBuilding
          )}
          {renderYearlyInputs(
            "fixedAssets",
            "plantMachinery",
            "Plant & Machinery",
            data.fixedAssets.plantMachinery
          )}
          {renderYearlyInputs(
            "fixedAssets",
            "fittingsFurniture",
            "Fittings and Furniture",
            data.fixedAssets.fittingsFurniture
          )}
          {renderYearlyInputs(
            "fixedAssets",
            "vehicles",
            "Vehicles",
            data.fixedAssets.vehicles
          )}
          {renderYearlyInputs(
            "fixedAssets",
            "officeEquipments",
            "Office equipments, Computer etc",
            data.fixedAssets.officeEquipments
          )}
          {renderYearlyInputs(
            "fixedAssets",
            "advanceAgainstCapitalGoods",
            "Advance against capital goods / Others",
            data.fixedAssets.advanceAgainstCapitalGoods
          )}
          {renderSectionTotal(
            "Total Fixed Assets (F)",
            calculateSectionTotal(data.fixedAssets)
          )}

          {/* Non Current Assets Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>G. Non Current Assets</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "nonCurrentAssets",
            "investment",
            "Investment",
            data.nonCurrentAssets.investment
          )}
          {renderYearlyInputs(
            "nonCurrentAssets",
            "deferredTaxOthers",
            "Deferred tax / others",
            data.nonCurrentAssets.deferredTaxOthers
          )}
          {renderSectionTotal(
            "Total of Non Current Assets (G)",
            calculateSectionTotal(data.nonCurrentAssets)
          )}

          {/* Others Section */}
          <tr className="section-header">
            <td colSpan={4}>
              <strong>H. Others</strong>
            </td>
          </tr>
          {renderYearlyInputs(
            "others",
            "otherAdvance",
            "Other advance",
            data.others.otherAdvance
          )}
          {renderYearlyInputs(
            "others",
            "buildingDepositOtherDeposit",
            "Building Deposit / Other Deposit",
            data.others.buildingDepositOtherDeposit
          )}
          {renderYearlyInputs(
            "others",
            "inventoryProject",
            "Inventory project",
            data.others.inventoryProject
          )}
          {renderSectionTotal(
            "Total of (H)",
            calculateSectionTotal(data.others)
          )}

          {/* Total Assets */}
          {(() => {
            const totalAssets: YearlyData = {
              previousYear:
                calculateSectionTotal(data.currentAssets).previousYear +
                calculateSectionTotal(data.fixedAssets).previousYear +
                calculateSectionTotal(data.nonCurrentAssets).previousYear +
                calculateSectionTotal(data.others).previousYear,
              currentYear:
                calculateSectionTotal(data.currentAssets).currentYear +
                calculateSectionTotal(data.fixedAssets).currentYear +
                calculateSectionTotal(data.nonCurrentAssets).currentYear +
                calculateSectionTotal(data.others).currentYear,
              projectedYear:
                calculateSectionTotal(data.currentAssets).projectedYear +
                calculateSectionTotal(data.fixedAssets).projectedYear +
                calculateSectionTotal(data.nonCurrentAssets).projectedYear +
                calculateSectionTotal(data.others).projectedYear,
            };
            return renderSectionTotal("Total Assets (E+F+G+H)", totalAssets);
          })()}
        </tbody>
      </table>
    </div>
  );
};

export default BalanceSheetForm;
