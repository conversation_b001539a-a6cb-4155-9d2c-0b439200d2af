# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Stripe Configuration (for payment processing)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Email Configuration (for notifications)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Application Configuration
VITE_APP_URL=http://localhost:5174
VITE_API_URL=http://localhost:5174/api

# Analytics (optional)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# File Upload Configuration
VITE_MAX_FILE_SIZE=10485760  # 10MB in bytes
VITE_ALLOWED_FILE_TYPES=application/pdf,image/jpeg,image/png,image/jpg

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_PAYMENTS=true
