import React from "react";
import {
  BalanceSheetData,
  ProfitLossData,
  YearlyData,
} from "../types/FinancialData";
import "./AnalysisResults.css";

interface Props {
  balanceSheetData: BalanceSheetData;
  profitLossData: ProfitLossData;
}

const AnalysisResults: React.FC<Props> = ({
  balanceSheetData,
  profitLossData,
}) => {
  // Helper function to calculate section totals
  const calculateSectionTotal = (sectionData: any): YearlyData => {
    const total: YearlyData = {
      previousYear: 0,
      currentYear: 0,
      projectedYear: 0,
    };
    Object.values(sectionData).forEach((fieldData: any) => {
      total.previousYear += fieldData.previousYear || 0;
      total.currentYear += fieldData.currentYear || 0;
      total.projectedYear += fieldData.projectedYear || 0;
    });
    return total;
  };

  // Calculate key totals
  const currentLiabilitiesTotal = calculateSectionTotal(
    balanceSheetData.currentLiabilities
  );
  const currentAssetsTotal = calculateSectionTotal(
    balanceSheetData.currentAssets
  );
  const totalSales: YearlyData = {
    previousYear:
      profitLossData.grossSalesDomestic.previousYear +
      profitLossData.exportSales.previousYear +
      profitLossData.commissionSales.previousYear,
    currentYear:
      profitLossData.grossSalesDomestic.currentYear +
      profitLossData.exportSales.currentYear +
      profitLossData.commissionSales.currentYear,
    projectedYear:
      profitLossData.grossSalesDomestic.projectedYear +
      profitLossData.exportSales.projectedYear +
      profitLossData.commissionSales.projectedYear,
  };

  // Calculate TNW (Total Net Worth)
  const tnw = calculateSectionTotal(balanceSheetData.capitalAndReserve);

  // Calculate TOL (Total Outside Liabilities)
  const tol: YearlyData = {
    previousYear:
      currentLiabilitiesTotal.previousYear +
      calculateSectionTotal(balanceSheetData.branchSisterConcerns)
        .previousYear +
      calculateSectionTotal(balanceSheetData.longTermLiabilities).previousYear,
    currentYear:
      currentLiabilitiesTotal.currentYear +
      calculateSectionTotal(balanceSheetData.branchSisterConcerns).currentYear +
      calculateSectionTotal(balanceSheetData.longTermLiabilities).currentYear,
    projectedYear:
      currentLiabilitiesTotal.projectedYear +
      calculateSectionTotal(balanceSheetData.branchSisterConcerns)
        .projectedYear +
      calculateSectionTotal(balanceSheetData.longTermLiabilities).projectedYear,
  };

  // P&L Analysis Calculations
  const plAnalysis = {
    pbdit: {
      previousYear:
        profitLossData.netProfit.previousYear +
        profitLossData.interest.previousYear +
        profitLossData.tax.previousYear +
        profitLossData.depreciation.previousYear,
      currentYear:
        profitLossData.netProfit.currentYear +
        profitLossData.interest.currentYear +
        profitLossData.tax.currentYear +
        profitLossData.depreciation.currentYear,
      projectedYear:
        profitLossData.netProfit.projectedYear +
        profitLossData.interest.projectedYear +
        profitLossData.tax.projectedYear +
        profitLossData.depreciation.projectedYear,
    },
    pbit: {
      previousYear:
        profitLossData.netProfit.previousYear +
        profitLossData.interest.previousYear,
      currentYear:
        profitLossData.netProfit.currentYear +
        profitLossData.interest.currentYear,
      projectedYear:
        profitLossData.netProfit.projectedYear +
        profitLossData.interest.projectedYear,
    },
    cashProfit: {
      previousYear:
        profitLossData.netProfit.previousYear +
        profitLossData.depreciation.previousYear,
      currentYear:
        profitLossData.netProfit.currentYear +
        profitLossData.depreciation.currentYear,
      projectedYear:
        profitLossData.netProfit.projectedYear +
        profitLossData.depreciation.projectedYear,
    },
  };

  // Ratio Analysis Calculations
  const ratioAnalysis = {
    currentRatio: {
      previousYear: currentLiabilitiesTotal.previousYear
        ? currentAssetsTotal.previousYear / currentLiabilitiesTotal.previousYear
        : 0,
      currentYear: currentLiabilitiesTotal.currentYear
        ? currentAssetsTotal.currentYear / currentLiabilitiesTotal.currentYear
        : 0,
      projectedYear: currentLiabilitiesTotal.projectedYear
        ? currentAssetsTotal.projectedYear /
          currentLiabilitiesTotal.projectedYear
        : 0,
    },
    tolQeToTnw: {
      previousYear: tnw.previousYear ? tol.previousYear / tnw.previousYear : 0,
      currentYear: tnw.currentYear ? tol.currentYear / tnw.currentYear : 0,
      projectedYear: tnw.projectedYear
        ? tol.projectedYear / tnw.projectedYear
        : 0,
    },
    netWorkingCapital: {
      previousYear:
        currentAssetsTotal.previousYear - currentLiabilitiesTotal.previousYear,
      currentYear:
        currentAssetsTotal.currentYear - currentLiabilitiesTotal.currentYear,
      projectedYear:
        currentAssetsTotal.projectedYear -
        currentLiabilitiesTotal.projectedYear,
    },
    velocityOfTurnover: {
      previousYear: currentAssetsTotal.previousYear
        ? totalSales.previousYear / currentAssetsTotal.previousYear
        : 0,
      currentYear: currentAssetsTotal.currentYear
        ? totalSales.currentYear / currentAssetsTotal.currentYear
        : 0,
      projectedYear: currentAssetsTotal.projectedYear
        ? totalSales.projectedYear / currentAssetsTotal.projectedYear
        : 0,
    },
    salesToTnw: {
      previousYear: tnw.previousYear
        ? totalSales.previousYear / tnw.previousYear
        : 0,
      currentYear: tnw.currentYear
        ? totalSales.currentYear / tnw.currentYear
        : 0,
      projectedYear: tnw.projectedYear
        ? totalSales.projectedYear / tnw.projectedYear
        : 0,
    },
    tradeDebtorsOutstanding: {
      previousYear: totalSales.previousYear
        ? (balanceSheetData.currentAssets.receivables.previousYear * 365) /
          totalSales.previousYear
        : 0,
      currentYear: totalSales.currentYear
        ? (balanceSheetData.currentAssets.receivables.currentYear * 365) /
          totalSales.currentYear
        : 0,
      projectedYear: totalSales.projectedYear
        ? (balanceSheetData.currentAssets.receivables.projectedYear * 365) /
          totalSales.projectedYear
        : 0,
    },
    tradeCreditorsOutstanding: {
      previousYear: totalSales.previousYear
        ? (balanceSheetData.currentLiabilities.tradeCreditors.previousYear *
            365) /
          totalSales.previousYear
        : 0,
      currentYear: totalSales.currentYear
        ? (balanceSheetData.currentLiabilities.tradeCreditors.currentYear *
            365) /
          totalSales.currentYear
        : 0,
      projectedYear: totalSales.projectedYear
        ? (balanceSheetData.currentLiabilities.tradeCreditors.projectedYear *
            365) /
          totalSales.projectedYear
        : 0,
    },
    closingStockHeld: {
      previousYear: totalSales.previousYear
        ? (balanceSheetData.currentAssets.stock.previousYear * 365) /
          totalSales.previousYear
        : 0,
      currentYear: totalSales.currentYear
        ? (balanceSheetData.currentAssets.stock.currentYear * 365) /
          totalSales.currentYear
        : 0,
      projectedYear: totalSales.projectedYear
        ? (balanceSheetData.currentAssets.stock.projectedYear * 365) /
          totalSales.projectedYear
        : 0,
    },
  };

  // Working Capital Assessment (for projected year only)
  const workingCapitalAssessment = {
    estimatedSalesTurnover: totalSales.projectedYear,
    totalCurrentAssets: currentAssetsTotal.projectedYear,
    currentLiabilitiesOtherThanBank:
      currentLiabilitiesTotal.projectedYear -
      balanceSheetData.currentLiabilities.shortTermBankFinance.projectedYear,
    workingCapitalGap:
      currentAssetsTotal.projectedYear -
      (currentLiabilitiesTotal.projectedYear -
        balanceSheetData.currentLiabilities.shortTermBankFinance.projectedYear),
    marginHigher: Math.max(
      currentAssetsTotal.projectedYear * 0.25,
      ratioAnalysis.netWorkingCapital.currentYear
    ),
    maxPermissibleBankFinance: 0, // Will be calculated below
  };

  workingCapitalAssessment.maxPermissibleBankFinance =
    workingCapitalAssessment.workingCapitalGap -
    workingCapitalAssessment.marginHigher;

  const renderAnalysisRow = (
    label: string,
    data: YearlyData,
    isPercentage = false,
    isDays = false
  ) => (
    <tr>
      <td className="field-label">{label}</td>
      <td>
        {isPercentage
          ? `${(data.previousYear * 100).toFixed(2)}%`
          : isDays
          ? `${data.previousYear.toFixed(0)} days`
          : data.previousYear.toLocaleString()}
      </td>
      <td>
        {isPercentage
          ? `${(data.currentYear * 100).toFixed(2)}%`
          : isDays
          ? `${data.currentYear.toFixed(0)} days`
          : data.currentYear.toLocaleString()}
      </td>
      <td>
        {isPercentage
          ? `${(data.projectedYear * 100).toFixed(2)}%`
          : isDays
          ? `${data.projectedYear.toFixed(0)} days`
          : data.projectedYear.toLocaleString()}
      </td>
    </tr>
  );

  return (
    <div className="analysis-results">
      <h2>Financial Analysis Results</h2>

      {/* P&L Analysis */}
      <section className="analysis-section">
        <h3>Analysis of P&L</h3>
        <table className="financial-table">
          <thead>
            <tr>
              <th>Metric</th>
              <th>Previous Year</th>
              <th>Current Year</th>
              <th>Projected Year</th>
            </tr>
          </thead>
          <tbody>
            {renderAnalysisRow(
              "PBDIT (Profit before dep., interest and tax)",
              plAnalysis.pbdit
            )}
            {renderAnalysisRow(
              "PBIT (Profit before interest and tax)",
              plAnalysis.pbit
            )}
            {renderAnalysisRow("Cash Profit", plAnalysis.cashProfit)}
          </tbody>
        </table>
      </section>

      {/* Ratio Analysis */}
      <section className="analysis-section">
        <h3>Ratio Analysis</h3>
        <table className="financial-table">
          <thead>
            <tr>
              <th>Ratio</th>
              <th>Previous Year</th>
              <th>Current Year</th>
              <th>Projected Year</th>
            </tr>
          </thead>
          <tbody>
            {renderAnalysisRow("Current Ratio", ratioAnalysis.currentRatio)}
            {renderAnalysisRow("(TOL + QE)/TNW", ratioAnalysis.tolQeToTnw)}
            {renderAnalysisRow(
              "Net Working Capital",
              ratioAnalysis.netWorkingCapital
            )}
            {renderAnalysisRow(
              "Velocity of turnover to Current Assets",
              ratioAnalysis.velocityOfTurnover
            )}
            {renderAnalysisRow("Sales / TNW", ratioAnalysis.salesToTnw)}
            {renderAnalysisRow(
              "Trade Debtors outstanding",
              ratioAnalysis.tradeDebtorsOutstanding,
              false,
              true
            )}
            {renderAnalysisRow(
              "Trade Creditors outstanding",
              ratioAnalysis.tradeCreditorsOutstanding,
              false,
              true
            )}
            {renderAnalysisRow(
              "Closing stock held by the party",
              ratioAnalysis.closingStockHeld,
              false,
              true
            )}
          </tbody>
        </table>
      </section>

      {/* Working Capital Assessment */}
      <section className="analysis-section">
        <h3>Working Capital Assessment (Projected Balance Sheet)</h3>
        <table className="financial-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr className="section-header">
              <td colSpan={2}>
                <strong>II Method of lending</strong>
              </td>
            </tr>
            <tr>
              <td>a) Estimated sales turnover</td>
              <td>
                {workingCapitalAssessment.estimatedSalesTurnover.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>b) Total current assets (TCA)</td>
              <td>
                {workingCapitalAssessment.totalCurrentAssets.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>c) Current liabilities other than bank borrowings</td>
              <td>
                {workingCapitalAssessment.currentLiabilitiesOtherThanBank.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>d) Working capital gap (WCG) = (b-c)</td>
              <td>
                {workingCapitalAssessment.workingCapitalGap.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>
                e) Margin higher of = MAX(25% on TCA : Available
                NetWorkingCapital)
              </td>
              <td>{workingCapitalAssessment.marginHigher.toLocaleString()}</td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;&nbsp;i) 25% on TCA</td>
              <td>
                {(
                  workingCapitalAssessment.totalCurrentAssets * 0.25
                ).toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;&nbsp;ii) Available NetWorkingCapital (NWC) as
                on Actual Year
              </td>
              <td>
                {ratioAnalysis.netWorkingCapital.currentYear.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>f) Maximum Permissible Bank Finance</td>
              <td>
                {workingCapitalAssessment.maxPermissibleBankFinance.toLocaleString()}
              </td>
            </tr>
          </tbody>
        </table>

        <h4>Margin money requirement</h4>
        <table className="financial-table">
          <tbody>
            <tr>
              <td>a. Working capital Gap</td>
              <td>
                {workingCapitalAssessment.workingCapitalGap.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>b. Less: Bank finance (Loan Approached by Customer)</td>
              <td>
                <input
                  type="number"
                  placeholder="Enter loan amount"
                  style={{ width: "200px", padding: "5px" }}
                />
              </td>
            </tr>
            <tr>
              <td>Margin required for Working capital = (a-b)</td>
              <td>To be calculated based on loan input</td>
            </tr>
          </tbody>
        </table>

        <h4>Availability of margin money</h4>
        <table className="financial-table">
          <tbody>
            <tr>
              <td>1. Available NWC as on Current Year</td>
              <td>
                {ratioAnalysis.netWorkingCapital.currentYear.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>2. Add: Increase in capital</td>
              <td>
                {(
                  balanceSheetData.capitalAndReserve.paidUpCapital
                    .projectedYear -
                  balanceSheetData.capitalAndReserve.paidUpCapital.currentYear
                ).toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>3. Add: Increase in quasi equity</td>
              <td>0</td>
            </tr>
            <tr>
              <td>4. Add: Increase in other long term loans</td>
              <td>
                {(
                  calculateSectionTotal(balanceSheetData.longTermLiabilities)
                    .projectedYear -
                  calculateSectionTotal(balanceSheetData.longTermLiabilities)
                    .currentYear
                ).toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>5. Add: Depreciation</td>
              <td>
                {profitLossData.depreciation.projectedYear.toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>6. Add: Increase / decrease in non-current assets</td>
              <td>
                {(
                  calculateSectionTotal(balanceSheetData.nonCurrentAssets)
                    .projectedYear -
                  calculateSectionTotal(balanceSheetData.nonCurrentAssets)
                    .currentYear
                ).toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>7. Add: Increase / decrease in lending to associates</td>
              <td>
                {(
                  calculateSectionTotal(balanceSheetData.branchSisterConcerns)
                    .projectedYear -
                  calculateSectionTotal(balanceSheetData.branchSisterConcerns)
                    .currentYear
                ).toLocaleString()}
              </td>
            </tr>
            <tr>
              <td>8. Add: Increase / decrease in fixed assets</td>
              <td>
                {(
                  calculateSectionTotal(balanceSheetData.fixedAssets)
                    .projectedYear -
                  calculateSectionTotal(balanceSheetData.fixedAssets)
                    .currentYear
                ).toLocaleString()}
              </td>
            </tr>
            <tr className="section-total">
              <td>
                <strong>Available margin money Sum(1:8)</strong>
              </td>
              <td>
                <strong>
                  {(
                    ratioAnalysis.netWorkingCapital.currentYear +
                    (balanceSheetData.capitalAndReserve.paidUpCapital
                      .projectedYear -
                      balanceSheetData.capitalAndReserve.paidUpCapital
                        .currentYear) +
                    (calculateSectionTotal(balanceSheetData.longTermLiabilities)
                      .projectedYear -
                      calculateSectionTotal(
                        balanceSheetData.longTermLiabilities
                      ).currentYear) +
                    profitLossData.depreciation.projectedYear +
                    (calculateSectionTotal(balanceSheetData.nonCurrentAssets)
                      .projectedYear -
                      calculateSectionTotal(balanceSheetData.nonCurrentAssets)
                        .currentYear) +
                    (calculateSectionTotal(
                      balanceSheetData.branchSisterConcerns
                    ).projectedYear -
                      calculateSectionTotal(
                        balanceSheetData.branchSisterConcerns
                      ).currentYear) +
                    (calculateSectionTotal(balanceSheetData.fixedAssets)
                      .projectedYear -
                      calculateSectionTotal(balanceSheetData.fixedAssets)
                        .currentYear)
                  ).toLocaleString()}
                </strong>
              </td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  );
};

export default AnalysisResults;
