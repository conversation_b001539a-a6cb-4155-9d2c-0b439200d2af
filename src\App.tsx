import React, { useState, useEffect } from "react";
import "./App.css";
import LandingPage from "./components/LandingPage";
import LoginPage from "./components/auth/LoginPage";
import RegisterPage from "./components/auth/RegisterPage";
import PricingPage from "./components/subscription/PricingPage";
import UserHomePage from "./components/UserHomePage";
import CompanySetup from "./components/CompanySetup";
import AnalysisChoiceComponent from "./components/AnalysisChoice";
import BalanceSheetCreator from "./components/BalanceSheetCreator";
import UploadBalanceSheet from "./components/UploadBalanceSheet";
import HorizontalBalanceSheet from "./components/HorizontalBalanceSheet";
import BalanceSheetForm from "./components/BalanceSheetForm";
import ProfitLossForm from "./components/ProfitLossForm";
import AnalysisResults from "./components/AnalysisResults";
import Contact from "./components/Contact";
import Pricing from "./components/Pricing";
import {
  createSession,
  getSession,
  clearSession,
  getCurrentUser,
  isAuthenticated as checkAuthenticated,
  initSessionMonitoring,
  updateActivity,
} from "./utils/sessionManager";
import {
  BalanceSheetData,
  ProfitLossData,
  CompanyInfo,
  BalanceSheetYear,
  AnalysisChoice,
} from "./types/FinancialData";
import {
  User,
  LoginCredentials,
  RegisterData,
  SubscriptionPlanType,
  TEST_USER_CREDENTIALS,
  TEST_USER_PROFILE,
} from "./types/Auth";
import { runAllTests } from "./utils/testSupabase";

function App() {
  const [currentStep, setCurrentStep] = useState<
    | "landing"
    | "login"
    | "register"
    | "pricing"
    | "contact"
    | "user-home"
    | "company-setup"
    | "analysis-choice"
    | "choice-selection"
    | "balance-sheet-entry"
    | "upload-analysis"
    | "balance-sheet-creator"
    | "analysis"
  >("landing");

  const [company, setCompany] = useState<CompanyInfo | null>(null);
  const [savedYearData, setSavedYearData] = useState<BalanceSheetYear[]>([]);
  const [analysisChoice, setAnalysisChoice] = useState<AnalysisChoice | null>(
    null
  );

  const [currentTab, setCurrentTab] = useState<
    "balance-sheet" | "profit-loss" | "analysis"
  >("balance-sheet");
  const [balanceSheetData, setBalanceSheetData] = useState<BalanceSheetData>({
    currentLiabilities: {
      tradeCreditors: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      expensesDutyOtherPayables: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      lcCreditorsJobWork: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      advanceReceivedFromBuyers: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      shortTermBankFinance: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      provisions: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      taxPayable: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      commercialPaper: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      sdMaturityRepayableOneYear: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
    },
    branchSisterConcerns: {
      havingCreditFacilities: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      notHavingFacilities: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
    },
    longTermLiabilities: {
      longTermFundsFromFamily: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      longTermLoanFromBank: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      longTermLoanFromInstitutions: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      cautionDeposit: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    },
    capitalAndReserve: {
      paidUpCapital: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      reservesAndSurplus: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      convertiblePreferenceShare: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      plAccount: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      lessMiscExpensesDrawings: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
    },
    currentAssets: {
      cashAndBankBalance: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      stock: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      receivables: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      shortTermAdvance: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      otherCurrentAsset: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      depositAdvances: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      others: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    },
    fixedAssets: {
      landAndBuilding: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      plantMachinery: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      fittingsFurniture: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      vehicles: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      officeEquipments: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      advanceAgainstCapitalGoods: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
    },
    nonCurrentAssets: {
      investment: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      deferredTaxOthers: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    },
    others: {
      otherAdvance: { previousYear: 0, currentYear: 0, projectedYear: 0 },
      buildingDepositOtherDeposit: {
        previousYear: 0,
        currentYear: 0,
        projectedYear: 0,
      },
      inventoryProject: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    },
  });

  const [profitLossData, setProfitLossData] = useState<ProfitLossData>({
    grossSalesDomestic: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    exportSales: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    commissionSales: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    interest: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    tax: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    depreciation: { previousYear: 0, currentYear: 0, projectedYear: 0 },
    netProfit: { previousYear: 0, currentYear: 0, projectedYear: 0 },
  });

  // Authentication state
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [sessionCleanup, setSessionCleanup] = useState<(() => void) | null>(
    null
  );

  // Initialize session management and check for existing session
  useEffect(() => {
    // Check for existing session
    const existingSession = getSession();
    if (existingSession) {
      setUser(existingSession.user);
      setIsAuthenticated(true);
      console.log("🔄 Restored session for user:", existingSession.user.email);
    }

    // Initialize session monitoring
    const cleanup = initSessionMonitoring(() => {
      console.log("🚪 Session expired, logging out...");
      handleLogout();
    });
    setSessionCleanup(() => cleanup);

    // Add global function to clear data (for development/testing)
    (window as any).clearBalanceSheetData = () => {
      localStorage.removeItem("companyInfo");
      localStorage.removeItem("balanceSheetData");
      localStorage.removeItem("skipCompanySetup");
      clearSession();
      window.location.reload();
    };

    // Add global function to test conversion (for development/testing)
    (window as any).testConversion = () => {
      console.log("Current savedYearData:", savedYearData);
      const converted = convertToOldFormat();
      console.log("Converted data:", converted);
    };

    // Test Supabase connection on app load
    runAllTests().then((success) => {
      if (success) {
        console.log("🎉 Supabase backend is ready!");
      } else {
        console.error(
          "❌ Supabase backend setup issues detected. Check console for details."
        );
      }
    });

    // Cleanup on unmount
    return () => {
      if (cleanup) cleanup();
    };
  }, []);

  // Check for existing company data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const savedCompany = localStorage.getItem("companyInfo");
      const skipSetup = localStorage.getItem("skipCompanySetup");

      // Only load saved company data if user is authenticated
      if (savedCompany && skipSetup === "true") {
        setCompany(JSON.parse(savedCompany));
        // Don't automatically redirect - let user choose from home page
      }
    }
  }, [isAuthenticated]);

  // Debug: Monitor savedYearData changes
  useEffect(() => {
    console.log("savedYearData changed:", savedYearData);
  }, [savedYearData]);

  // Authentication handlers
  const handleLogin = async (credentials: LoginCredentials) => {
    setAuthLoading(true);
    setAuthError(null);

    try {
      // Check for test user credentials
      if (
        credentials.email === TEST_USER_CREDENTIALS.email &&
        credentials.password === TEST_USER_CREDENTIALS.password
      ) {
        // Test user with full access
        await new Promise((resolve) => setTimeout(resolve, 500)); // Shorter delay for test user

        setUser(TEST_USER_PROFILE);
        setIsAuthenticated(true);

        // Create session for test user
        createSession(TEST_USER_PROFILE);

        // Show success message for test user
        console.log("🎉 Test user logged in successfully with full access!");

        // Check if company data exists, if so go to home, otherwise company setup
        const savedCompany = localStorage.getItem("companyInfo");
        const skipSetup = localStorage.getItem("skipCompanySetup");

        if (savedCompany && skipSetup === "true") {
          setCompany(JSON.parse(savedCompany));
          setCurrentStep("user-home");
        } else {
          setCurrentStep("company-setup");
        }
        return;
      }

      // Mock authentication for other users - replace with real API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if credentials are valid (basic validation)
      if (!credentials.email || !credentials.password) {
        throw new Error("Invalid credentials");
      }

      // Mock user data for regular users
      const mockUser: User = {
        id: "1",
        email: credentials.email,
        firstName: "John",
        lastName: "Doe",
        role: "user",
        subscription: {
          type: "free",
          status: "active",
          startDate: new Date(),
          endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day
          features: {
            maxAnalyses: 1,
            maxCompanies: 1,
            exportToExcel: false,
            prioritySupport: false,
            advancedAnalytics: false,
            apiAccess: false,
            customReports: false,
          },
          usageCount: 0,
          maxUsage: 1,
        },
        isEmailVerified: true,
        createdAt: new Date(),
      };

      setUser(mockUser);
      setIsAuthenticated(true);

      // Create session for regular user
      createSession(mockUser);

      // Check if company data exists, if so go to home, otherwise company setup
      const savedCompany = localStorage.getItem("companyInfo");
      const skipSetup = localStorage.getItem("skipCompanySetup");

      if (savedCompany && skipSetup === "true") {
        setCompany(JSON.parse(savedCompany));
        setCurrentStep("user-home");
      } else {
        setCurrentStep("company-setup");
      }
    } catch (error) {
      setAuthError("Invalid email or password");
    } finally {
      setAuthLoading(false);
    }
  };

  const handleRegister = async (data: RegisterData) => {
    setAuthLoading(true);
    setAuthError(null);

    try {
      // Mock registration - replace with real API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Mock user data
      const mockUser: User = {
        id: "1",
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: "user",
        subscription: {
          type: "free",
          status: "active",
          startDate: new Date(),
          endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day
          features: {
            maxAnalyses: 1,
            maxCompanies: 1,
            exportToExcel: false,
            prioritySupport: false,
            advancedAnalytics: false,
            apiAccess: false,
            customReports: false,
          },
          usageCount: 0,
          maxUsage: 1,
        },
        isEmailVerified: true,
        createdAt: new Date(),
        company: data.company,
        phone: data.phone,
      };

      setUser(mockUser);
      setIsAuthenticated(true);

      // Create session for registered user
      createSession(mockUser);

      // Check if company data exists, if so go to home, otherwise company setup
      const savedCompany = localStorage.getItem("companyInfo");
      const skipSetup = localStorage.getItem("skipCompanySetup");

      if (savedCompany && skipSetup === "true") {
        setCompany(JSON.parse(savedCompany));
        setCurrentStep("user-home");
      } else {
        setCurrentStep("company-setup");
      }
    } catch (error) {
      setAuthError("Registration failed. Please try again.");
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSelectPlan = (planType: SubscriptionPlanType) => {
    // Mock plan selection - replace with real payment processing
    console.log("Selected plan:", planType);

    if (planType === "free") {
      // Check if company data exists, if so go to home, otherwise company setup
      const savedCompany = localStorage.getItem("companyInfo");
      const skipSetup = localStorage.getItem("skipCompanySetup");

      if (savedCompany && skipSetup === "true") {
        setCompany(JSON.parse(savedCompany));
        setCurrentStep("user-home");
      } else {
        setCurrentStep("company-setup");
      }
    } else {
      // In real implementation, redirect to payment processor
      alert(`Redirecting to payment for ${planType} plan...`);
    }
  };

  const handleCompanySetup = (companyInfo: CompanyInfo) => {
    setCompany(companyInfo);
    localStorage.setItem("skipCompanySetup", "true");
    setCurrentStep("user-home");
  };

  const handleLogout = () => {
    console.log("🚪 User logging out...");

    // Clear session
    clearSession();

    // Clear application state
    setUser(null);
    setIsAuthenticated(false);
    setCompany(null);
    setSavedYearData([]);

    // Clear local storage
    localStorage.removeItem("companyInfo");
    localStorage.removeItem("balanceSheetData");
    localStorage.removeItem("skipCompanySetup");

    // Cleanup session monitoring
    if (sessionCleanup) {
      sessionCleanup();
      setSessionCleanup(null);
    }

    // Redirect to landing page
    setCurrentStep("landing");

    console.log("✅ Logout complete");
  };

  const handleBackToHome = () => {
    setCurrentStep("user-home");
  };

  const handleAnalyzeBalanceSheet = () => {
    if (company) {
      setCurrentStep("analysis-choice");
    } else {
      setCurrentStep("company-setup");
    }
  };

  const handleCreateBalanceSheet = () => {
    if (company) {
      setCurrentStep("balance-sheet-creator");
    } else {
      setCurrentStep("company-setup");
    }
  };

  const handleNewCompany = () => {
    // Clear existing company data and go to company setup
    setCompany(null);
    localStorage.removeItem("companyInfo");
    localStorage.removeItem("skipCompanySetup");
    setCurrentStep("company-setup");
  };

  const handleChoiceSelect = (choice: AnalysisChoice) => {
    setAnalysisChoice(choice);
    if (choice === "analyze-existing") {
      setCurrentStep("balance-sheet-entry");
    } else if (choice === "upload-analysis") {
      setCurrentStep("upload-analysis");
    } else {
      setCurrentStep("balance-sheet-creator");
    }
  };

  const handleBackToChoice = () => {
    setCurrentStep("analysis-choice");
    setAnalysisChoice(null);
  };

  const handleUploadDataExtracted = (extractedData: BalanceSheetYear[]) => {
    console.log("Upload data extracted:", extractedData);

    // Save the extracted data all at once
    setSavedYearData((prev) => {
      const newData = [...prev];

      extractedData.forEach((yearData) => {
        const existingIndex = newData.findIndex(
          (data) => data.year === yearData.year
        );
        if (existingIndex >= 0) {
          newData[existingIndex] = yearData;
        } else {
          newData.push(yearData);
        }
      });

      console.log("Final saved year data:", newData);
      return newData;
    });

    // Go directly to analysis after a small delay to ensure state is updated
    setTimeout(() => {
      setCurrentStep("analysis");
    }, 100);
  };

  const handleYearDataSave = (yearData: BalanceSheetYear) => {
    setSavedYearData((prev) => {
      const existingIndex = prev.findIndex(
        (data) => data.year === yearData.year
      );
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = yearData;
        return updated;
      } else {
        return [...prev, yearData];
      }
    });

    // Save to localStorage
    const allData = [...savedYearData, yearData];
    localStorage.setItem("balanceSheetData", JSON.stringify(allData));
  };

  const handleBalanceSheetComplete = () => {
    setCurrentStep("analysis");
  };

  const handleStartNewAnalysis = () => {
    // Clear all stored data
    localStorage.removeItem("companyInfo");
    localStorage.removeItem("balanceSheetData");
    localStorage.removeItem("skipCompanySetup");

    // Reset state
    setCompany(null);
    setSavedYearData([]);
    setCurrentStep("company-setup");
  };

  const convertToOldFormat = (): {
    balanceSheetData: BalanceSheetData;
    profitLossData: ProfitLossData;
  } => {
    // Convert savedYearData (from uploads/manual entry) to old format for analysis
    const defaultData = {
      previousYear: 0,
      currentYear: 0,
      projectedYear: 0,
    };

    // If we have uploaded/manual data, convert it
    if (savedYearData.length > 0) {
      console.log("Converting savedYearData to old format:", savedYearData);
      const convertedBalanceSheet: BalanceSheetData = {
        currentLiabilities: {
          tradeCreditors: { ...defaultData },
          expensesDutyOtherPayables: { ...defaultData },
          lcCreditorsJobWork: { ...defaultData },
          advanceReceivedFromBuyers: { ...defaultData },
          shortTermBankFinance: { ...defaultData },
          provisions: { ...defaultData },
          taxPayable: { ...defaultData },
          commercialPaper: { ...defaultData },
          sdMaturityRepayableOneYear: { ...defaultData },
        },
        branchSisterConcerns: {
          havingCreditFacilities: { ...defaultData },
          notHavingFacilities: { ...defaultData },
        },
        longTermLiabilities: {
          longTermFundsFromFamily: { ...defaultData },
          longTermLoanFromBank: { ...defaultData },
          longTermLoanFromInstitutions: { ...defaultData },
          cautionDeposit: { ...defaultData },
        },
        capitalAndReserve: {
          paidUpCapital: { ...defaultData },
          reservesAndSurplus: { ...defaultData },
          convertiblePreferenceShare: { ...defaultData },
          plAccount: { ...defaultData },
          lessMiscExpensesDrawings: { ...defaultData },
        },
        currentAssets: {
          cashAndBankBalance: { ...defaultData },
          stock: { ...defaultData },
          receivables: { ...defaultData },
          shortTermAdvance: { ...defaultData },
          otherCurrentAsset: { ...defaultData },
          depositAdvances: { ...defaultData },
          others: { ...defaultData },
        },
        fixedAssets: {
          landAndBuilding: { ...defaultData },
          plantMachinery: { ...defaultData },
          fittingsFurniture: { ...defaultData },
          vehicles: { ...defaultData },
          officeEquipments: { ...defaultData },
          advanceAgainstCapitalGoods: { ...defaultData },
        },
        nonCurrentAssets: {
          investment: { ...defaultData },
          deferredTaxOthers: { ...defaultData },
        },
        others: {
          otherAdvance: { ...defaultData },
          buildingDepositOtherDeposit: { ...defaultData },
          inventoryProject: { ...defaultData },
        },
      };

      const convertedProfitLoss: ProfitLossData = {
        grossSalesDomestic: { ...defaultData },
        exportSales: { ...defaultData },
        commissionSales: { ...defaultData },
        interest: { ...defaultData },
        tax: { ...defaultData },
        depreciation: { ...defaultData },
        netProfit: { ...defaultData },
      };

      // Map each year's data to the appropriate year column
      savedYearData.forEach((yearData) => {
        const yearKey =
          yearData.yearType === "Previous Year (Audited)"
            ? "previousYear"
            : yearData.yearType === "Current Year (Actual)"
            ? "currentYear"
            : "projectedYear";

        // Convert liabilities
        Object.keys(convertedBalanceSheet.currentLiabilities).forEach((key) => {
          if (key in yearData.liabilities.currentLiabilities) {
            (convertedBalanceSheet.currentLiabilities as any)[key][yearKey] =
              yearData.liabilities.currentLiabilities[key];
          }
        });

        Object.keys(convertedBalanceSheet.branchSisterConcerns).forEach(
          (key) => {
            if (key in yearData.liabilities.branchSisterConcerns) {
              (convertedBalanceSheet.branchSisterConcerns as any)[key][
                yearKey
              ] = yearData.liabilities.branchSisterConcerns[key];
            }
          }
        );

        Object.keys(convertedBalanceSheet.longTermLiabilities).forEach(
          (key) => {
            if (key in yearData.liabilities.longTermLiabilities) {
              (convertedBalanceSheet.longTermLiabilities as any)[key][yearKey] =
                yearData.liabilities.longTermLiabilities[key];
            }
          }
        );

        Object.keys(convertedBalanceSheet.capitalAndReserve).forEach((key) => {
          if (key in yearData.liabilities.capitalAndReserve) {
            (convertedBalanceSheet.capitalAndReserve as any)[key][yearKey] =
              yearData.liabilities.capitalAndReserve[key];
          }
        });

        // Convert assets
        Object.keys(convertedBalanceSheet.currentAssets).forEach((key) => {
          if (key in yearData.assets.currentAssets) {
            (convertedBalanceSheet.currentAssets as any)[key][yearKey] =
              yearData.assets.currentAssets[key];
          }
        });

        Object.keys(convertedBalanceSheet.fixedAssets).forEach((key) => {
          if (key in yearData.assets.fixedAssets) {
            (convertedBalanceSheet.fixedAssets as any)[key][yearKey] =
              yearData.assets.fixedAssets[key];
          }
        });

        Object.keys(convertedBalanceSheet.nonCurrentAssets).forEach((key) => {
          if (key in yearData.assets.nonCurrentAssets) {
            (convertedBalanceSheet.nonCurrentAssets as any)[key][yearKey] =
              yearData.assets.nonCurrentAssets[key];
          }
        });

        Object.keys(convertedBalanceSheet.others).forEach((key) => {
          if (key in yearData.assets.others) {
            (convertedBalanceSheet.others as any)[key][yearKey] =
              yearData.assets.others[key];
          }
        });

        // Convert profit & loss
        Object.keys(convertedProfitLoss).forEach((key) => {
          if (key in yearData.profitLoss) {
            (convertedProfitLoss as any)[key][yearKey] =
              yearData.profitLoss[key];
          }
        });
      });

      console.log("Converted balance sheet data:", convertedBalanceSheet);
      console.log("Converted profit loss data:", convertedProfitLoss);

      return {
        balanceSheetData: convertedBalanceSheet,
        profitLossData: convertedProfitLoss,
      };
    }

    // Fallback to existing data if no uploaded data
    return {
      balanceSheetData,
      profitLossData,
    };
  };

  // Show landing page
  if (currentStep === "landing") {
    return (
      <LandingPage
        onGetStarted={() => setCurrentStep("register")}
        onLogin={() => setCurrentStep("login")}
        onPricing={() => setCurrentStep("pricing")}
        onContact={() => setCurrentStep("contact")}
      />
    );
  }

  // Show contact page
  if (currentStep === "contact") {
    return <Contact onBack={() => setCurrentStep("landing")} />;
  }

  // Show pricing page (new standalone pricing)
  if (currentStep === "pricing") {
    return <Pricing onBack={() => setCurrentStep("landing")} />;
  }

  // Show login page
  if (currentStep === "login") {
    return (
      <LoginPage
        onLogin={handleLogin}
        onRegister={() => setCurrentStep("register")}
        onForgotPassword={() =>
          alert("Forgot password functionality coming soon!")
        }
        onBackToLanding={() => setCurrentStep("landing")}
        isLoading={authLoading}
        error={authError}
      />
    );
  }

  // Show register page
  if (currentStep === "register") {
    return (
      <RegisterPage
        onRegister={handleRegister}
        onLogin={() => setCurrentStep("login")}
        onBackToLanding={() => setCurrentStep("landing")}
        isLoading={authLoading}
        error={authError}
      />
    );
  }

  // Show pricing page
  if (currentStep === "pricing") {
    return (
      <PricingPage
        onSelectPlan={handleSelectPlan}
        onBack={() => setCurrentStep("landing")}
        currentPlan={user?.subscription.type}
      />
    );
  }

  // Show user home page after authentication
  if (currentStep === "user-home" && isAuthenticated && user) {
    return (
      <UserHomePage
        user={user}
        company={company}
        onAnalyzeBalanceSheet={handleAnalyzeBalanceSheet}
        onCreateBalanceSheet={handleCreateBalanceSheet}
        onLogout={handleLogout}
        onSettings={() => alert("Settings coming soon!")}
        onNewCompany={handleNewCompany}
      />
    );
  }

  // Show company setup if no company is set
  if (currentStep === "company-setup") {
    return (
      <CompanySetup
        onCompanySetup={handleCompanySetup}
        onBack={isAuthenticated ? handleBackToHome : undefined}
        showBackButton={isAuthenticated}
      />
    );
  }

  // Show analysis choice for balance sheet analysis
  if (currentStep === "analysis-choice" && company) {
    return (
      <AnalysisChoiceComponent
        company={company}
        onChoiceSelect={handleChoiceSelect}
        onBack={handleBackToHome}
        showBackButton={true}
      />
    );
  }

  // Show choice selection after company setup (legacy)
  if (currentStep === "choice-selection" && company) {
    return (
      <AnalysisChoiceComponent
        company={company}
        onChoiceSelect={handleChoiceSelect}
      />
    );
  }

  // Show upload balance sheet for AI analysis
  if (currentStep === "upload-analysis" && company) {
    return (
      <UploadBalanceSheet
        company={company}
        onDataExtracted={handleUploadDataExtracted}
        onBack={handleBackToHome}
      />
    );
  }

  // Show balance sheet creator for new balance sheet
  if (currentStep === "balance-sheet-creator" && company) {
    return <BalanceSheetCreator company={company} onBack={handleBackToHome} />;
  }

  // Show horizontal balance sheet entry for analysis
  if (currentStep === "balance-sheet-entry" && company) {
    return (
      <HorizontalBalanceSheet
        company={company}
        onDataSave={handleYearDataSave}
        onComplete={handleBalanceSheetComplete}
        onBack={handleBackToHome}
      />
    );
  }

  // Show analysis results
  if (currentStep === "analysis") {
    console.log("Current savedYearData when showing analysis:", savedYearData);
    const { balanceSheetData: bsData, profitLossData: plData } =
      convertToOldFormat();

    return (
      <div className="app">
        <header className="app-header">
          <div className="header-content">
            <div className="header-left">
              <button
                className="btn-home"
                onClick={handleBackToChoice}
                title="Back to Options"
              >
                🏠 Home
              </button>
              <h1>Balance Sheet Analyzer - {company?.companyName}</h1>
            </div>
            <div className="header-right">
              <button
                className="btn-new-analysis"
                onClick={handleStartNewAnalysis}
                title="Start completely new analysis"
              >
                🔄 New Company
              </button>
            </div>
          </div>
          <nav className="tab-navigation">
            <button
              className={currentTab === "balance-sheet" ? "active" : ""}
              onClick={() => setCurrentTab("balance-sheet")}
            >
              Balance Sheet Data
            </button>
            <button
              className={currentTab === "profit-loss" ? "active" : ""}
              onClick={() => setCurrentTab("profit-loss")}
            >
              Profit & Loss Data
            </button>
            <button
              className={currentTab === "analysis" ? "active" : ""}
              onClick={() => setCurrentTab("analysis")}
            >
              Analysis Results
            </button>
          </nav>
        </header>

        <main className="app-main">
          {currentTab === "balance-sheet" && (
            <BalanceSheetForm
              data={bsData}
              onChange={(newData) => {
                // When user edits the form, we need to update the original state
                // and also update savedYearData if it exists
                setBalanceSheetData(newData);

                // TODO: Also update savedYearData to keep it in sync
                // This would require converting back from old format to new format
              }}
            />
          )}
          {currentTab === "profit-loss" && (
            <ProfitLossForm
              data={plData}
              onChange={(newData) => {
                setProfitLossData(newData);
                // TODO: Also update savedYearData for P&L
              }}
            />
          )}
          {currentTab === "analysis" && (
            <AnalysisResults
              balanceSheetData={bsData}
              profitLossData={plData}
            />
          )}
        </main>
      </div>
    );
  }

  return null;
}

export default App;
