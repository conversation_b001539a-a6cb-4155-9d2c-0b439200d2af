import React, { useState } from "react";
import {
  CompanyInfo,
  BalanceSheetYear,
  SingleYearData,
} from "../types/FinancialData";
import "./HorizontalBalanceSheet.css";

interface Props {
  company: CompanyInfo;
  onDataSave: (yearData: BalanceSheetYear) => void;
  onComplete: () => void;
  onBack?: () => void;
}

const HorizontalBalanceSheet: React.FC<Props> = ({
  company,
  onDataSave,
  onComplete,
  onBack,
}) => {
  const [currentYearIndex, setCurrentYearIndex] = useState(0);

  // Generate year types and years based on company's numberOfYears setting
  const generateYearTypes = (numberOfYears: number) => {
    const types: Array<
      | "Previous Year (Audited)"
      | "Current Year (Actual)"
      | "Projected Next Year"
    > = [];

    if (numberOfYears >= 2) {
      types.push("Previous Year (Audited)");
    }
    types.push("Current Year (Actual)");
    if (numberOfYears >= 3) {
      types.push("Projected Next Year");
    }

    // Add additional years if more than 3
    for (let i = 4; i <= numberOfYears; i++) {
      types.push("Projected Next Year" as const);
    }

    return types;
  };

  const generateYears = (numberOfYears: number) => {
    const currentYear = new Date().getFullYear();
    const years: string[] = [];

    // Start from previous years if numberOfYears >= 2
    const startYear = numberOfYears >= 2 ? currentYear - 1 : currentYear;

    for (let i = 0; i < numberOfYears; i++) {
      years.push((startYear + i).toString());
    }

    return years;
  };

  const yearTypes = generateYearTypes(company.numberOfYears);
  const years = generateYears(company.numberOfYears);

  const [yearData, setYearData] = useState<BalanceSheetYear>({
    year: years[currentYearIndex],
    yearType: yearTypes[currentYearIndex],
    liabilities: {
      currentLiabilities: {
        tradeCreditors: 0,
        expensesDutyOtherPayables: 0,
        lcCreditorsJobWork: 0,
        advanceReceivedFromBuyers: 0,
        shortTermBankFinance: 0,
        provisions: 0,
        taxPayable: 0,
        commercialPaper: 0,
        sdMaturityRepayableOneYear: 0,
      },
      branchSisterConcerns: {
        havingCreditFacilities: 0,
        notHavingFacilities: 0,
      },
      longTermLiabilities: {
        longTermFundsFromFamily: 0,
        longTermLoanFromBank: 0,
        longTermLoanFromInstitutions: 0,
        cautionDeposit: 0,
      },
      capitalAndReserve: {
        paidUpCapital: 0,
        reservesAndSurplus: 0,
        convertiblePreferenceShare: 0,
        plAccount: 0,
        lessMiscExpensesDrawings: 0,
      },
    },
    assets: {
      currentAssets: {
        cashAndBankBalance: 0,
        stock: 0,
        receivables: 0,
        shortTermAdvance: 0,
        otherCurrentAsset: 0,
        depositAdvances: 0,
        others: 0,
      },
      fixedAssets: {
        landAndBuilding: 0,
        plantMachinery: 0,
        fittingsFurniture: 0,
        vehicles: 0,
        officeEquipments: 0,
        advanceAgainstCapitalGoods: 0,
      },
      nonCurrentAssets: {
        investment: 0,
        deferredTaxOthers: 0,
      },
      others: {
        otherAdvance: 0,
        buildingDepositOtherDeposit: 0,
        inventoryProject: 0,
      },
    },
    profitLoss: {
      grossSalesDomestic: 0,
      exportSales: 0,
      commissionSales: 0,
      interest: 0,
      tax: 0,
      depreciation: 0,
      netProfit: 0,
    },
  });

  const updateValue = (
    section:
      | keyof BalanceSheetYear["liabilities"]
      | keyof BalanceSheetYear["assets"]
      | "profitLoss",
    subsection: string | null,
    field: string,
    value: number
  ) => {
    setYearData((prev) => {
      const newData = { ...prev };
      if (section === "profitLoss") {
        newData.profitLoss[field] = value;
      } else if (subsection) {
        if (section in newData.liabilities) {
          (
            newData.liabilities[
              section as keyof typeof newData.liabilities
            ] as SingleYearData
          )[field] = value;
        } else if (section in newData.assets) {
          (
            newData.assets[
              section as keyof typeof newData.assets
            ] as SingleYearData
          )[field] = value;
        }
      }
      return newData;
    });
  };

  const calculateSectionTotal = (sectionData: SingleYearData): number => {
    return Object.values(sectionData).reduce(
      (sum, value) => sum + (value || 0),
      0
    );
  };

  const handleNext = () => {
    // Save current year data
    onDataSave(yearData);

    if (currentYearIndex < yearTypes.length - 1) {
      const nextIndex = currentYearIndex + 1;
      setCurrentYearIndex(nextIndex);
      setYearData((prev) => ({
        ...prev,
        year: years[nextIndex],
        yearType: yearTypes[nextIndex],
      }));
    } else {
      // All years completed
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentYearIndex > 0) {
      const prevIndex = currentYearIndex - 1;
      setCurrentYearIndex(prevIndex);
      setYearData((prev) => ({
        ...prev,
        year: years[prevIndex],
        yearType: yearTypes[prevIndex],
      }));
    }
  };

  const renderInputField = (
    section:
      | keyof BalanceSheetYear["liabilities"]
      | keyof BalanceSheetYear["assets"]
      | "profitLoss",
    subsection: string | null,
    field: string,
    label: string,
    value: number
  ) => (
    <tr key={field}>
      <td className="field-label">{label}</td>
      <td className="amount-cell">
        <input
          type="number"
          value={value || ""}
          onChange={(e) =>
            updateValue(
              section,
              subsection,
              field,
              parseFloat(e.target.value) || 0
            )
          }
          className="amount-input"
          placeholder="0.00"
        />
      </td>
    </tr>
  );

  const renderSectionTotal = (label: string, total: number) => (
    <tr className="section-total">
      <td className="field-label">
        <strong>{label}</strong>
      </td>
      <td className="amount-cell">
        <strong>
          ₹ {total.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
        </strong>
      </td>
    </tr>
  );

  return (
    <div className="horizontal-balance-sheet">
      <div className="balance-sheet-header">
        {onBack && (
          <button onClick={onBack} className="back-to-options">
            🏠 Back to Home
          </button>
        )}
        <div className="company-info">
          <h1>{company.companyName}</h1>
          <p>{company.address}</p>
        </div>
        <div className="year-info">
          <h2>Balance Sheet - {yearData.yearType}</h2>
          <h3>For the year ending {yearData.year}</h3>
          <div className="progress-indicator">
            <span className="progress-text">
              Year {currentYearIndex + 1} of {company.numberOfYears}
            </span>
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{
                  width: `${
                    ((currentYearIndex + 1) / company.numberOfYears) * 100
                  }%`,
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div className="progress-indicator">
        <div className="progress-steps">
          {yearTypes.map((_, index) => (
            <div
              key={index}
              className={`step ${
                index <= currentYearIndex ? "completed" : ""
              } ${index === currentYearIndex ? "active" : ""}`}
            >
              <span className="step-number">{index + 1}</span>
              <span className="step-label">{yearTypes[index]}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="balance-sheet-content">
        <div className="balance-sheet-side">
          <h3 className="side-title">LIABILITIES</h3>

          <table className="balance-sheet-table">
            <tbody>
              {/* A. Current Liabilities */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>A. CURRENT LIABILITIES</strong>
                </td>
              </tr>
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "tradeCreditors",
                "Trade Creditors",
                yearData.liabilities.currentLiabilities.tradeCreditors
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "expensesDutyOtherPayables",
                "Expenses / Duty / Other payables",
                yearData.liabilities.currentLiabilities
                  .expensesDutyOtherPayables
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "lcCreditorsJobWork",
                "LC creditors / Creditors for job work",
                yearData.liabilities.currentLiabilities.lcCreditorsJobWork
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "advanceReceivedFromBuyers",
                "Advance received from buyers",
                yearData.liabilities.currentLiabilities
                  .advanceReceivedFromBuyers
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "shortTermBankFinance",
                "Short term bank finance",
                yearData.liabilities.currentLiabilities.shortTermBankFinance
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "provisions",
                "Provisions",
                yearData.liabilities.currentLiabilities.provisions
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "taxPayable",
                "Tax Payable",
                yearData.liabilities.currentLiabilities.taxPayable
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "commercialPaper",
                "Commercial Paper",
                yearData.liabilities.currentLiabilities.commercialPaper
              )}
              {renderInputField(
                "currentLiabilities",
                "currentLiabilities",
                "sdMaturityRepayableOneYear",
                "SD Maturity repayable one year",
                yearData.liabilities.currentLiabilities
                  .sdMaturityRepayableOneYear
              )}
              {renderSectionTotal(
                "Total Current Liabilities (A)",
                calculateSectionTotal(yearData.liabilities.currentLiabilities)
              )}

              {/* B. Branch / Sister Concerns */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>B. BRANCH / SISTER CONCERNS A/C</strong>
                </td>
              </tr>
              {renderInputField(
                "branchSisterConcerns",
                "branchSisterConcerns",
                "havingCreditFacilities",
                "Having credit facilities with us",
                yearData.liabilities.branchSisterConcerns.havingCreditFacilities
              )}
              {renderInputField(
                "branchSisterConcerns",
                "branchSisterConcerns",
                "notHavingFacilities",
                "Not having facilities with us",
                yearData.liabilities.branchSisterConcerns.notHavingFacilities
              )}
              {renderSectionTotal(
                "Total of (B)",
                calculateSectionTotal(yearData.liabilities.branchSisterConcerns)
              )}

              {/* C. Long Term Liabilities */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>C. LONG TERM LIABILITIES</strong>
                </td>
              </tr>
              {renderInputField(
                "longTermLiabilities",
                "longTermLiabilities",
                "longTermFundsFromFamily",
                "Long Term Funds from Family Members",
                yearData.liabilities.longTermLiabilities.longTermFundsFromFamily
              )}
              {renderInputField(
                "longTermLiabilities",
                "longTermLiabilities",
                "longTermLoanFromBank",
                "Long Term Loan from our bank",
                yearData.liabilities.longTermLiabilities.longTermLoanFromBank
              )}
              {renderInputField(
                "longTermLiabilities",
                "longTermLiabilities",
                "longTermLoanFromInstitutions",
                "Long Term Loan from Financial Institutions",
                yearData.liabilities.longTermLiabilities
                  .longTermLoanFromInstitutions
              )}
              {renderInputField(
                "longTermLiabilities",
                "longTermLiabilities",
                "cautionDeposit",
                "Caution deposit",
                yearData.liabilities.longTermLiabilities.cautionDeposit
              )}
              {renderSectionTotal(
                "Total Long Term Liabilities (C)",
                calculateSectionTotal(yearData.liabilities.longTermLiabilities)
              )}

              {/* D. Capital and Reserve */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>D. CAPITAL AND RESERVE</strong>
                </td>
              </tr>
              {renderInputField(
                "capitalAndReserve",
                "capitalAndReserve",
                "paidUpCapital",
                "Paid up Capital",
                yearData.liabilities.capitalAndReserve.paidUpCapital
              )}
              {renderInputField(
                "capitalAndReserve",
                "capitalAndReserve",
                "reservesAndSurplus",
                "Reserves and surplus",
                yearData.liabilities.capitalAndReserve.reservesAndSurplus
              )}
              {renderInputField(
                "capitalAndReserve",
                "capitalAndReserve",
                "convertiblePreferenceShare",
                "Convertible Preference Share",
                yearData.liabilities.capitalAndReserve
                  .convertiblePreferenceShare
              )}
              {renderInputField(
                "capitalAndReserve",
                "capitalAndReserve",
                "plAccount",
                "P & L account",
                yearData.liabilities.capitalAndReserve.plAccount
              )}
              {renderInputField(
                "capitalAndReserve",
                "capitalAndReserve",
                "lessMiscExpensesDrawings",
                "Less: Misc. Expenses / Drawings",
                yearData.liabilities.capitalAndReserve.lessMiscExpensesDrawings
              )}
              {renderSectionTotal(
                "Net Capital and Reserves (D)",
                calculateSectionTotal(yearData.liabilities.capitalAndReserve)
              )}

              {/* Total Liabilities */}
              <tr className="grand-total">
                <td className="field-label">
                  <strong>TOTAL LIABILITIES (A+B+C+D)</strong>
                </td>
                <td className="amount-cell">
                  <strong>
                    ₹{" "}
                    {(
                      calculateSectionTotal(
                        yearData.liabilities.currentLiabilities
                      ) +
                      calculateSectionTotal(
                        yearData.liabilities.branchSisterConcerns
                      ) +
                      calculateSectionTotal(
                        yearData.liabilities.longTermLiabilities
                      ) +
                      calculateSectionTotal(
                        yearData.liabilities.capitalAndReserve
                      )
                    ).toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                  </strong>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="balance-sheet-side">
          <h3 className="side-title">ASSETS</h3>

          <table className="balance-sheet-table">
            <tbody>
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>E. CURRENT ASSETS</strong>
                </td>
              </tr>
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "cashAndBankBalance",
                "Cash and Bank Balance",
                yearData.assets.currentAssets.cashAndBankBalance
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "stock",
                "Stock",
                yearData.assets.currentAssets.stock
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "receivables",
                "Receivables",
                yearData.assets.currentAssets.receivables
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "shortTermAdvance",
                "Short term advance",
                yearData.assets.currentAssets.shortTermAdvance
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "otherCurrentAsset",
                "Other current asset",
                yearData.assets.currentAssets.otherCurrentAsset
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "depositAdvances",
                "Deposit / Advances",
                yearData.assets.currentAssets.depositAdvances
              )}
              {renderInputField(
                "currentAssets",
                "currentAssets",
                "others",
                "Others (Viz. Pre paid expenses etc)",
                yearData.assets.currentAssets.others
              )}
              {renderSectionTotal(
                "Total Current Assets (E)",
                calculateSectionTotal(yearData.assets.currentAssets)
              )}

              {/* F. Fixed Assets */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>F. FIXED ASSETS</strong>
                </td>
              </tr>
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "landAndBuilding",
                "Land and Building",
                yearData.assets.fixedAssets.landAndBuilding
              )}
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "plantMachinery",
                "Plant & Machinery",
                yearData.assets.fixedAssets.plantMachinery
              )}
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "fittingsFurniture",
                "Fittings and Furniture",
                yearData.assets.fixedAssets.fittingsFurniture
              )}
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "vehicles",
                "Vehicles",
                yearData.assets.fixedAssets.vehicles
              )}
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "officeEquipments",
                "Office equipments, Computer etc",
                yearData.assets.fixedAssets.officeEquipments
              )}
              {renderInputField(
                "fixedAssets",
                "fixedAssets",
                "advanceAgainstCapitalGoods",
                "Advance against capital goods / Others",
                yearData.assets.fixedAssets.advanceAgainstCapitalGoods
              )}
              {renderSectionTotal(
                "Total Fixed Assets (F)",
                calculateSectionTotal(yearData.assets.fixedAssets)
              )}

              {/* G. Non Current Assets */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>G. NON CURRENT ASSETS</strong>
                </td>
              </tr>
              {renderInputField(
                "nonCurrentAssets",
                "nonCurrentAssets",
                "investment",
                "Investment",
                yearData.assets.nonCurrentAssets.investment
              )}
              {renderInputField(
                "nonCurrentAssets",
                "nonCurrentAssets",
                "deferredTaxOthers",
                "Deferred tax / others",
                yearData.assets.nonCurrentAssets.deferredTaxOthers
              )}
              {renderSectionTotal(
                "Total of Non Current Assets (G)",
                calculateSectionTotal(yearData.assets.nonCurrentAssets)
              )}

              {/* H. Others */}
              <tr className="section-header">
                <td colSpan={2}>
                  <strong>H. OTHERS</strong>
                </td>
              </tr>
              {renderInputField(
                "others",
                "others",
                "otherAdvance",
                "Other advance",
                yearData.assets.others.otherAdvance
              )}
              {renderInputField(
                "others",
                "others",
                "buildingDepositOtherDeposit",
                "Building Deposit / Other Deposit",
                yearData.assets.others.buildingDepositOtherDeposit
              )}
              {renderInputField(
                "others",
                "others",
                "inventoryProject",
                "Inventory project",
                yearData.assets.others.inventoryProject
              )}
              {renderSectionTotal(
                "Total of (H)",
                calculateSectionTotal(yearData.assets.others)
              )}

              {/* Total Assets */}
              <tr className="grand-total">
                <td className="field-label">
                  <strong>TOTAL ASSETS (E+F+G+H)</strong>
                </td>
                <td className="amount-cell">
                  <strong>
                    ₹{" "}
                    {(
                      calculateSectionTotal(yearData.assets.currentAssets) +
                      calculateSectionTotal(yearData.assets.fixedAssets) +
                      calculateSectionTotal(yearData.assets.nonCurrentAssets) +
                      calculateSectionTotal(yearData.assets.others)
                    ).toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                  </strong>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div className="navigation-controls">
        <button
          onClick={handlePrevious}
          disabled={currentYearIndex === 0}
          className="btn-secondary"
        >
          ← Previous Year
        </button>

        <div className="year-indicator">
          {currentYearIndex + 1} of {yearTypes.length}
        </div>

        <button onClick={handleNext} className="btn-primary">
          {currentYearIndex === yearTypes.length - 1
            ? "Complete & Analyze"
            : "Next Year →"}
        </button>
      </div>
    </div>
  );
};

export default HorizontalBalanceSheet;
