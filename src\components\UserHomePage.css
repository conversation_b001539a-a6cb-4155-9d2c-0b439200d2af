.user-home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Header Styles */
.home-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-section .brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  width: 32px;
  height: 32px;
  color: #3498db;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.main-nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.nav-link:hover,
.nav-link.active {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.user-plan {
  font-size: 0.75rem;
  color: #64748b;
}

.session-info {
  font-size: 0.7rem;
  color: #059669;
  font-weight: 500;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #475569;
  border-color: #cbd5e0;
}

.action-btn.logout {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.action-btn.logout:hover {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fca5a5;
  transform: translateY(-1px);
}

.action-icon {
  width: 20px;
  height: 20px;
  color: currentColor;
  stroke-width: 2;
}

/* Launch Offer Banner */
.launch-offer-banner {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 2rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 40;
  min-height: 80px;
  display: block !important;
  visibility: visible !important;
}

.offer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.offer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.offer-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

.offer-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.offer-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
}

.offer-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

.offer-btn {
  background: white;
  color: #f5576c;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.offer-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #f8f9fa;
}

.offer-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.offer-btn:hover .offer-arrow {
  transform: translateX(3px);
}

/* Create Balance Sheet Banner */
.create-balance-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
  position: relative;
  z-index: 30;
  min-height: 120px;
  display: block !important;
  visibility: visible !important;
}

.create-banner-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.create-banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.create-banner-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.create-banner-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.create-banner-icon-svg {
  width: 30px;
  height: 30px;
  color: white;
}

.create-banner-text {
  flex: 1;
}

.create-banner-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.create-banner-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
}

.create-banner-cta {
  background: white;
  color: #667eea;
  border: none;
  border-radius: 10px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.create-banner-cta:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.create-banner-arrow {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.create-banner-cta:hover .create-banner-arrow {
  transform: translateX(3px);
}

/* Main Content */
.home-main {
  padding: 0;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Welcome Section */
.welcome-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

.company-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  min-width: 300px;
}

.company-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.company-details {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.company-analysis {
  color: #059669;
  font-weight: 500;
  font-size: 0.85rem;
  margin: 0;
}

.company-stats {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon {
  width: 16px;
  height: 16px;
  color: #059669;
}

.stat-label {
  font-size: 0.85rem;
  color: #059669;
  font-weight: 500;
}

.new-company-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.new-company-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: translateY(-1px);
}

.new-company-icon {
  width: 14px;
  height: 14px;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.action-card.primary {
  border-color: #3498db;
}

.action-card.primary:hover {
  border-color: #2980b9;
  box-shadow: 0 20px 40px rgba(52, 152, 219, 0.2);
}

.action-card.secondary {
  border-color: #27ae60;
}

.action-card.secondary:hover {
  border-color: #229954;
  box-shadow: 0 20px 40px rgba(39, 174, 96, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.card-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon-wrapper.analyze {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.card-icon-wrapper.create {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.card-icon {
  width: 28px;
  height: 28px;
  color: white;
}

.card-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.card-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #3498db;
  font-weight: 600;
}

.action-arrow {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.action-card:hover .action-arrow {
  transform: translateX(5px);
}

/* Recent Activity */
.recent-activity {
  margin-bottom: 2rem;
}

.activity-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activity-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.activity-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.activity-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.activity-description {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.activity-time {
  color: #94a3b8;
  font-size: 0.8rem;
}

/* Features Overview */
.features-overview {
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-3px);
}

.feature-icon {
  width: 40px;
  height: 40px;
  color: #3498db;
  margin-bottom: 1rem;
}

.feature-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.feature-desc {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .main-nav {
    gap: 1rem;
  }

  .main-container {
    padding: 0 1rem;
  }

  .welcome-section {
    flex-direction: column;
    text-align: center;
  }

  .offer-container {
    padding: 0 1rem;
  }

  .offer-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .offer-title {
    font-size: 1rem;
  }

  .offer-subtitle {
    font-size: 0.8rem;
  }

  .offer-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
    justify-content: center;
  }

  .create-banner-container {
    padding: 0 1rem;
  }

  .create-banner-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .create-banner-left {
    flex-direction: column;
    gap: 1rem;
  }

  .create-banner-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto;
  }

  .create-banner-icon-svg {
    width: 25px;
    height: 25px;
  }

  .create-banner-title {
    font-size: 1.1rem;
  }

  .create-banner-description {
    font-size: 0.85rem;
  }

  .create-banner-cta {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    justify-content: center;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
