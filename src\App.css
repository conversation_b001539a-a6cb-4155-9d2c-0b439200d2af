.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.app-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.btn-home {
  background-color: #27ae60;
  border: 2px solid #27ae60;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-home:hover {
  background-color: #229954;
  border-color: #229954;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.tab-navigation {
  display: flex;
  gap: 1rem;
}

.tab-navigation button {
  background-color: transparent;
  border: 2px solid #34495e;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.tab-navigation button:hover {
  background-color: #34495e;
  transform: translateY(-2px);
}

.tab-navigation button.active {
  background-color: #3498db;
  border-color: #3498db;
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.btn-new-analysis {
  background-color: #e74c3c;
  border: 2px solid #e74c3c;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-new-analysis:hover {
  background-color: #c0392b;
  border-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.app-main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.financial-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.financial-table th {
  background-color: #34495e;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #2c3e50;
}

.financial-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ecf0f1;
}

.financial-table tr:hover {
  background-color: #f8f9fa;
}

.field-label {
  font-weight: 500;
  color: #2c3e50;
  min-width: 300px;
}

.section-header td {
  background-color: #ecf0f1;
  font-weight: 600;
  color: #2c3e50;
  padding: 1rem;
  border-top: 2px solid #bdc3c7;
}

.section-total td {
  background-color: #e8f4f8;
  font-weight: 600;
  color: #2c3e50;
  border-top: 2px solid #3498db;
  border-bottom: 2px solid #3498db;
}

.financial-table input[type="number"] {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  font-size: 1rem;
  text-align: right;
  transition: border-color 0.3s ease;
}

.financial-table input[type="number"]:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.analysis-section {
  margin-bottom: 3rem;
}

.analysis-section h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
}

.analysis-section h4 {
  color: #34495e;
  font-size: 1.2rem;
  margin: 2rem 0 1rem 0;
}

@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .header-right {
    align-self: flex-end;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }

  .tab-navigation button {
    width: 100%;
    text-align: center;
  }

  .financial-table {
    font-size: 0.9rem;
  }

  .financial-table th,
  .financial-table td {
    padding: 0.5rem;
  }

  .field-label {
    min-width: auto;
  }
}
