.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.auth-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1200px;
  width: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  min-height: 600px;
}

/* Left Side - Branding */
.auth-branding {
  background: linear-gradient(135deg, #1a365d 0%, #2c5aa0 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.auth-branding::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.branding-content {
  position: relative;
  z-index: 1;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  color: white;
  z-index: 10;
  position: relative;
}

.brand-icon {
  color: #63b3ed !important;
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.branding-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.branding-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.branding-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.branding-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.feature-check {
  color: #68d391;
  flex-shrink: 0;
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.security-icon {
  color: #63b3ed;
  flex-shrink: 0;
}

.security-text {
  display: flex;
  flex-direction: column;
}

.security-title {
  font-weight: 600;
  font-size: 0.9rem;
}

.security-subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Launch Offer Card */
.launch-offer-card {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  text-align: center;
}

.offer-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.offer-icon {
  size: 16px;
}

.offer-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.offer-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.current-price {
  font-size: 2rem;
  font-weight: 800;
}

.original-price {
  font-size: 1.2rem;
  text-decoration: line-through;
  opacity: 0.7;
}

.offer-description {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 1rem;
}

.offer-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.offer-feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

/* Testimonial Mini */
.testimonial-mini {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #63b3ed;
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.testimonial-author {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.author-title {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Right Side - Form */
.auth-form-section {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.auth-form-container {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.back-to-landing {
  background: none;
  border: none;
  color: #718096;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
}

.back-to-landing:hover {
  color: white !important;
  background: rgba(113, 128, 150, 0.2);
  transform: translateY(-1px);
}

.auth-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  color: #718096;
  line-height: 1.5;
}

.auth-error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
  margin-bottom: 0.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  margin-bottom: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: #2d3748 !important;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: block;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 1.125rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718096 !important;
  width: 18px;
  height: 18px;
  z-index: 2;
  pointer-events: none;
  flex-shrink: 0;
}

.form-input {
  width: 100%;
  padding: 0.875rem 3.5rem 0.875rem 3.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.2;
  transition: all 0.3s ease;
  background: white !important;
  color: #2d3748 !important;
  font-weight: 500;
  box-sizing: border-box;
  min-height: 48px;
}

.form-input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-input::placeholder {
  color: #9ca3af !important;
  font-weight: 400;
  opacity: 1;
  font-size: 0.95rem;
  text-indent: 0;
}

.form-input:focus::placeholder {
  color: #d1d5db !important;
  opacity: 0.7;
}

.form-input.error {
  border-color: #e53e3e;
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af !important;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease, background-color 0.2s ease;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin: 0;
}

.password-toggle:hover {
  color: #6b7280 !important;
  background: rgba(107, 114, 128, 0.08);
  transform: translateY(-50%) scale(1);
}

.field-error {
  color: #e53e3e;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-input {
  width: 16px;
  height: 16px;
  accent-color: #3182ce;
}

.checkbox-text {
  font-size: 0.9rem;
  color: #4a5568;
}

.checkbox-link {
  color: #3182ce;
  text-decoration: none;
}

.checkbox-link:hover {
  text-decoration: underline;
}

.forgot-password {
  background: none;
  border: none;
  color: #3182ce;
  font-size: 0.9rem;
  cursor: pointer;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.auth-submit-btn {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border: none;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(49, 130, 206, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.auth-divider {
  text-align: center;
  position: relative;
  margin: 1.5rem 0;
}

.auth-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.auth-divider span {
  background: white;
  color: #a0aec0;
  padding: 0 1rem;
  font-size: 0.9rem;
}

.auth-alternative {
  text-align: center;
}

.alternative-text {
  color: #718096;
  margin-bottom: 0.5rem;
}

.alternative-btn {
  background: none;
  border: none;
  color: #3182ce;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

.alternative-btn:hover {
  text-decoration: underline;
}

.auth-footer {
  margin-top: 2rem;
  text-align: center;
}

.footer-text {
  font-size: 0.8rem;
  color: #a0aec0;
  line-height: 1.5;
}

.footer-link {
  color: #3182ce;
  text-decoration: none;
}

.footer-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-page {
    padding: 1rem;
  }

  .auth-container {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .auth-branding {
    padding: 2rem;
    text-align: center;
  }

  .branding-title {
    font-size: 1.5rem;
  }

  .auth-form-section {
    padding: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* Button animations */
.btn-icon {
  transition: transform 0.3s ease;
}

.auth-submit-btn:hover .btn-icon {
  transform: translateX(3px);
}

/* Text Styling Fixes */
.auth-link {
  color: #3182ce !important;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #2c5aa0 !important;
  text-decoration: underline;
}

.auth-text {
  color: #4a5568 !important;
  font-weight: 500;
}

.auth-description {
  color: #718096 !important;
  font-size: 0.95rem;
  line-height: 1.5;
}

.auth-header h2 {
  color: #2d3748 !important;
  font-weight: 600;
}

.auth-header p {
  color: #718096 !important;
}

/* Additional Input Fixes */
.form-input::-webkit-input-placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-size: 0.95rem;
}

.form-input::-moz-placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-size: 0.95rem;
}

.form-input:-ms-input-placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-size: 0.95rem;
}

.form-input:-moz-placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-size: 0.95rem;
}

/* Ensure input text is always visible */
.form-input {
  -webkit-text-fill-color: #2d3748 !important;
}

.form-input:focus {
  -webkit-text-fill-color: #2d3748 !important;
}

/* Input wrapper improvements */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

/* Ensure proper spacing between icon and text */
.form-input[type="text"],
.form-input[type="email"],
.form-input[type="tel"],
.form-input[type="password"] {
  padding-left: 3.25rem !important;
  text-indent: 0 !important;
}

/* Fix for password fields with toggle button */
.input-wrapper:has(.password-toggle) .form-input {
  padding-right: 3.5rem !important;
}

/* Additional password toggle fixes */
.password-toggle:focus {
  outline: none;
  color: #6b7280 !important;
  background: rgba(107, 114, 128, 0.1);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
  transition: transform 0.1s ease;
}

/* Test User Info Styles */
.test-user-info {
  margin: 1.5rem 0;
}

.test-user-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.25rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.test-user-title {
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.test-user-desc {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.test-credentials {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid #e2e8f0;
}

.credential-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.credential-item:last-child {
  margin-bottom: 0;
}

.credential-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.credential-value {
  background: #f1f5f9;
  color: #1e293b;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid #cbd5e1;
}

.test-user-features {
  color: #059669;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0.75rem 0 1rem 0;
}

.test-fill-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.test-fill-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.test-fill-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}
