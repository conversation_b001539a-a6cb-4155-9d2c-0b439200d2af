import React, { useState, useEffect } from "react";
import {
  CompanyInfo,
  BalanceSheetTotals,
  BalanceSheetYear,
} from "../types/FinancialData";
import {
  generateOptimalBalanceSheet,
  calculateFinancialRatios,
} from "../utils/balanceSheetGenerator";
import "./BalanceSheetCreator.css";

interface Props {
  company: CompanyInfo;
  onBack: () => void;
}

const BalanceSheetCreator: React.FC<Props> = ({ company, onBack }) => {
  const [totals, setTotals] = useState<BalanceSheetTotals>({
    totalAssets: 0,
    totalLiabilities: 0,
    year: new Date().getFullYear().toString(),
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedBalanceSheet, setGeneratedBalanceSheet] =
    useState<BalanceSheetYear | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Generate balance sheet whenever totals change
  useEffect(() => {
    if (totals.totalAssets > 0 || totals.totalLiabilities > 0) {
      const balancedTotal = Math.max(
        totals.totalAssets,
        totals.totalLiabilities
      );
      if (balancedTotal > 0) {
        const generated = generateOptimalBalanceSheet(
          totals.totalAssets,
          totals.totalLiabilities,
          company,
          totals.year
        );
        setGeneratedBalanceSheet(generated);
      }
    } else {
      setGeneratedBalanceSheet(null);
    }
  }, [totals, company]);

  const handleInputChange = (
    field: keyof BalanceSheetTotals,
    value: string | number
  ) => {
    setTotals((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const generatePDF = async () => {
    if (!generatedBalanceSheet) {
      alert("Please enter valid amounts to generate balance sheet");
      return;
    }

    setIsGenerating(true);

    try {
      // Create a new window for PDF generation
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        alert("Please allow popups to generate PDF");
        return;
      }

      const ratios = calculateFinancialRatios(generatedBalanceSheet);

      const pdfContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Balance Sheet - ${company.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .title { font-size: 18px; margin-bottom: 5px; }
            .year { font-size: 14px; color: #666; }
            .balance-sheet { width: 100%; border-collapse: collapse; margin-top: 20px; }
            .balance-sheet th, .balance-sheet td { border: 1px solid #000; padding: 8px; text-align: left; }
            .balance-sheet th { background-color: #f0f0f0; font-weight: bold; }
            .section-header { background-color: #e0e0e0; font-weight: bold; }
            .amount { text-align: right; }
            .total-row { font-weight: bold; background-color: #f5f5f5; }
            .side { width: 50%; vertical-align: top; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">${company.companyName}</div>
            <div class="title">BALANCE SHEET</div>
            <div class="year">As at ${totals.year}</div>
          </div>
          
          <table class="balance-sheet">
            <tr>
              <td class="side">
                <table style="width: 100%; border: none;">
                  <tr><th colspan="2" style="text-align: center;">LIABILITIES</th></tr>
                  <tr class="section-header"><td colspan="2">A. CURRENT LIABILITIES</td></tr>
                  <tr><td>Trade Creditors</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.tradeCreditors.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Expenses / Duty / Other payables</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.expensesDutyOtherPayables.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>LC creditors / Creditors for job work</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.lcCreditorsJobWork.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Advance received from buyers</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.advanceReceivedFromBuyers.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Short term bank finance</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.shortTermBankFinance.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Provisions</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.provisions.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Tax Payable</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.taxPayable.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Commercial Paper</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.commercialPaper.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>SD Maturity repayable one year</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.currentLiabilities.sdMaturityRepayableOneYear.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total Current Liabilities (A)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.liabilities.currentLiabilities
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr class="section-header"><td colspan="2">B. BRANCH / SISTER CONCERNS A/C</td></tr>
                  <tr><td>Having credit facilities with us</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.branchSisterConcerns.havingCreditFacilities.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Not having facilities with us</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.branchSisterConcerns.notHavingFacilities.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total of (B)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.liabilities.branchSisterConcerns
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr class="section-header"><td colspan="2">C. LONG TERM LIABILITIES</td></tr>
                  <tr><td>Long Term Funds from Family Members</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.longTermLiabilities.longTermFundsFromFamily.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Long Term Loan from our bank</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.longTermLiabilities.longTermLoanFromBank.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Long Term Loan from Financial Institutions</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.longTermLiabilities.longTermLoanFromInstitutions.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Caution deposit</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.longTermLiabilities.cautionDeposit.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total Long Term Liabilities (C)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.liabilities.longTermLiabilities
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr class="section-header"><td colspan="2">D. CAPITAL AND RESERVE</td></tr>
                  <tr><td>Paid up Capital</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.capitalAndReserve.paidUpCapital.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Reserves and surplus</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.capitalAndReserve.reservesAndSurplus.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Convertible Preference Share</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.capitalAndReserve.convertiblePreferenceShare.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>P & L account</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.capitalAndReserve.plAccount.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Less: Misc. Expenses / Drawings</td><td class="amount">₹ ${generatedBalanceSheet.liabilities.capitalAndReserve.lessMiscExpensesDrawings.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Net Capital and Reserves (D)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.liabilities.capitalAndReserve
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr style="background-color: #d4edda; font-weight: bold; font-size: 14px;">
                    <td><strong>TOTAL LIABILITIES (A+B+C+D)</strong></td>
                    <td class="amount"><strong>₹ ${totals.totalLiabilities.toLocaleString(
                      "en-IN"
                    )}</strong></td>
                  </tr>
                </table>
              </td>
              <td class="side">
                <table style="width: 100%; border: none;">
                  <tr><th colspan="2" style="text-align: center;">ASSETS</th></tr>
                  <tr class="section-header"><td colspan="2">E. CURRENT ASSETS</td></tr>
                  <tr><td>Cash and Bank Balance</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.cashAndBankBalance.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Stock</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.stock.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Receivables</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.receivables.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Short term advance</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.shortTermAdvance.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Other current asset</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.otherCurrentAsset.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Deposit / Advances</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.depositAdvances.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Others (Viz. Pre paid expenses etc)</td><td class="amount">₹ ${generatedBalanceSheet.assets.currentAssets.others.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total Current Assets (E)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.assets.currentAssets
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr class="section-header"><td colspan="2">F. FIXED ASSETS</td></tr>
                  <tr><td>Land and Building</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.landAndBuilding.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Plant & Machinery</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.plantMachinery.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Fittings and Furniture</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.fittingsFurniture.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Vehicles</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.vehicles.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Office equipments, Computer etc</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.officeEquipments.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Advance against capital goods / Others</td><td class="amount">₹ ${generatedBalanceSheet.assets.fixedAssets.advanceAgainstCapitalGoods.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total Fixed Assets (F)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.assets.fixedAssets
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr class="section-header"><td colspan="2">G. NON CURRENT ASSETS</td></tr>
                  <tr><td>Investment</td><td class="amount">₹ ${generatedBalanceSheet.assets.nonCurrentAssets.investment.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Deferred tax / others</td><td class="amount">₹ ${generatedBalanceSheet.assets.nonCurrentAssets.deferredTaxOthers.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total of Non Current Assets (G)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.assets.nonCurrentAssets
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>

                  <tr class="section-header"><td colspan="2">H. OTHERS</td></tr>
                  <tr><td>Other advance</td><td class="amount">₹ ${generatedBalanceSheet.assets.others.otherAdvance.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Building Deposit / Other Deposit</td><td class="amount">₹ ${generatedBalanceSheet.assets.others.buildingDepositOtherDeposit.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr><td>Inventory project</td><td class="amount">₹ ${generatedBalanceSheet.assets.others.inventoryProject.toLocaleString(
                    "en-IN"
                  )}</td></tr>
                  <tr class="total-row"><td><strong>Total of (H)</strong></td><td class="amount"><strong>₹ ${Object.values(
                    generatedBalanceSheet.assets.others
                  )
                    .reduce((sum, val) => sum + val, 0)
                    .toLocaleString("en-IN")}</strong></td></tr>
                  
                  <tr style="background-color: #d4edda; font-weight: bold; font-size: 14px;">
                    <td><strong>TOTAL ASSETS (E+F+G+H)</strong></td>
                    <td class="amount"><strong>₹ ${totals.totalAssets.toLocaleString(
                      "en-IN"
                    )}</strong></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
          
          <div class="footer">
            <p><strong>Note:</strong> This is a template balance sheet. Please fill in the actual amounts for each line item.</p>
            <p>Generated by Balance Sheet Analyzer on ${new Date().toLocaleDateString()}</p>
            <p>Company: ${company.companyName} | Contact: ${
        company.email
      } | Phone: ${company.phone}</p>
          </div>
        </body>
        </html>
      `;

      printWindow.document.write(pdfContent);
      printWindow.document.close();

      // Wait for content to load then print
      setTimeout(() => {
        printWindow.print();
      }, 500);
    } catch (error) {
      console.error("Error generating PDF:", error);
      alert("Error generating PDF. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateExcel = () => {
    if (!generatedBalanceSheet) {
      alert("Please enter valid amounts to generate balance sheet");
      return;
    }

    setIsGenerating(true);

    try {
      // Create CSV content with real data
      const csvContent = `Balance Sheet - ${company.companyName}
As at ${totals.year}

LIABILITIES,Amount,ASSETS,Amount
A. CURRENT LIABILITIES,,E. CURRENT ASSETS,
Trade Creditors,${
        generatedBalanceSheet.liabilities.currentLiabilities.tradeCreditors
      },Cash and Bank Balance,${
        generatedBalanceSheet.assets.currentAssets.cashAndBankBalance
      }
Expenses / Duty / Other payables,${
        generatedBalanceSheet.liabilities.currentLiabilities
          .expensesDutyOtherPayables
      },Stock,${generatedBalanceSheet.assets.currentAssets.stock}
LC creditors / Creditors for job work,${
        generatedBalanceSheet.liabilities.currentLiabilities.lcCreditorsJobWork
      },Receivables,${generatedBalanceSheet.assets.currentAssets.receivables}
Advance received from buyers,${
        generatedBalanceSheet.liabilities.currentLiabilities
          .advanceReceivedFromBuyers
      },Short term advance,${
        generatedBalanceSheet.assets.currentAssets.shortTermAdvance
      }
Short term bank finance,${
        generatedBalanceSheet.liabilities.currentLiabilities
          .shortTermBankFinance
      },Other current asset,${
        generatedBalanceSheet.assets.currentAssets.otherCurrentAsset
      }
Provisions,${
        generatedBalanceSheet.liabilities.currentLiabilities.provisions
      },Deposit / Advances,${
        generatedBalanceSheet.assets.currentAssets.depositAdvances
      }
Tax Payable,${
        generatedBalanceSheet.liabilities.currentLiabilities.taxPayable
      },Others (Viz. Pre paid expenses etc),${
        generatedBalanceSheet.assets.currentAssets.others
      }
Commercial Paper,${
        generatedBalanceSheet.liabilities.currentLiabilities.commercialPaper
      },Total Current Assets (E),${Object.values(
        generatedBalanceSheet.assets.currentAssets
      ).reduce((sum, val) => sum + val, 0)}
SD Maturity repayable one year,${
        generatedBalanceSheet.liabilities.currentLiabilities
          .sdMaturityRepayableOneYear
      },,
Total Current Liabilities (A),${Object.values(
        generatedBalanceSheet.liabilities.currentLiabilities
      ).reduce((sum, val) => sum + val, 0)},F. FIXED ASSETS,
,,Land and Building,${generatedBalanceSheet.assets.fixedAssets.landAndBuilding}
B. BRANCH / SISTER CONCERNS A/C,,Plant & Machinery,${
        generatedBalanceSheet.assets.fixedAssets.plantMachinery
      }
Having credit facilities with us,${
        generatedBalanceSheet.liabilities.branchSisterConcerns
          .havingCreditFacilities
      },Fittings and Furniture,${
        generatedBalanceSheet.assets.fixedAssets.fittingsFurniture
      }
Not having facilities with us,${
        generatedBalanceSheet.liabilities.branchSisterConcerns
          .notHavingFacilities
      },Vehicles,${generatedBalanceSheet.assets.fixedAssets.vehicles}
Total of (B),${Object.values(
        generatedBalanceSheet.liabilities.branchSisterConcerns
      ).reduce((sum, val) => sum + val, 0)},Office equipments Computer etc,${
        generatedBalanceSheet.assets.fixedAssets.officeEquipments
      }
,,Advance against capital goods / Others,${
        generatedBalanceSheet.assets.fixedAssets.advanceAgainstCapitalGoods
      }
C. LONG TERM LIABILITIES,,Total Fixed Assets (F),${Object.values(
        generatedBalanceSheet.assets.fixedAssets
      ).reduce((sum, val) => sum + val, 0)}
Long Term Funds from Family Members,${
        generatedBalanceSheet.liabilities.longTermLiabilities
          .longTermFundsFromFamily
      },,
Long Term Loan from our bank,${
        generatedBalanceSheet.liabilities.longTermLiabilities
          .longTermLoanFromBank
      },G. NON CURRENT ASSETS,
Long Term Loan from Financial Institutions,${
        generatedBalanceSheet.liabilities.longTermLiabilities
          .longTermLoanFromInstitutions
      },Investment,${generatedBalanceSheet.assets.nonCurrentAssets.investment}
Caution deposit,${
        generatedBalanceSheet.liabilities.longTermLiabilities.cautionDeposit
      },Deferred tax / others,${
        generatedBalanceSheet.assets.nonCurrentAssets.deferredTaxOthers
      }
Total Long Term Liabilities (C),${Object.values(
        generatedBalanceSheet.liabilities.longTermLiabilities
      ).reduce(
        (sum, val) => sum + val,
        0
      )},Total of Non Current Assets (G),${Object.values(
        generatedBalanceSheet.assets.nonCurrentAssets
      ).reduce((sum, val) => sum + val, 0)}
,,
D. CAPITAL AND RESERVE,,H. OTHERS,
Paid up Capital,${
        generatedBalanceSheet.liabilities.capitalAndReserve.paidUpCapital
      },Other advance,${generatedBalanceSheet.assets.others.otherAdvance}
Reserves and surplus,${
        generatedBalanceSheet.liabilities.capitalAndReserve.reservesAndSurplus
      },Building Deposit / Other Deposit,${
        generatedBalanceSheet.assets.others.buildingDepositOtherDeposit
      }
Convertible Preference Share,${
        generatedBalanceSheet.liabilities.capitalAndReserve
          .convertiblePreferenceShare
      },Inventory project,${
        generatedBalanceSheet.assets.others.inventoryProject
      }
P & L account,${
        generatedBalanceSheet.liabilities.capitalAndReserve.plAccount
      },Total of (H),${Object.values(
        generatedBalanceSheet.assets.others
      ).reduce((sum, val) => sum + val, 0)}
Less: Misc. Expenses / Drawings,${
        generatedBalanceSheet.liabilities.capitalAndReserve
          .lessMiscExpensesDrawings
      },,
Net Capital and Reserves (D),${Object.values(
        generatedBalanceSheet.liabilities.capitalAndReserve
      ).reduce((sum, val) => sum + val, 0)},,
,,
TOTAL LIABILITIES (A+B+C+D),${totals.totalLiabilities},TOTAL ASSETS (E+F+G+H),${
        totals.totalAssets
      }

Generated by Balance Sheet Analyzer
Company: ${company.companyName}
Contact: ${company.email}
Phone: ${company.phone}
Date: ${new Date().toLocaleDateString()}`;

      // Create and download CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `Balance_Sheet_${company.companyName.replace(/\s+/g, "_")}_${
          totals.year
        }.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error generating Excel:", error);
      alert("Error generating Excel file. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const isBalanced = totals.totalAssets === totals.totalLiabilities;

  return (
    <div className="balance-sheet-creator">
      <div className="creator-container">
        <div className="creator-header">
          <button onClick={onBack} className="back-button">
            ← Back to Options
          </button>
          <h1>Create New Balance Sheet</h1>
          <h2>{company.companyName}</h2>
          <p>
            Enter your total assets and liabilities to generate a professional
            balance sheet template
          </p>
        </div>

        <div className="totals-input">
          <div className="input-group">
            <label htmlFor="year">Financial Year</label>
            <input
              type="text"
              id="year"
              value={totals.year}
              onChange={(e) => handleInputChange("year", e.target.value)}
              placeholder="2024"
            />
          </div>

          <div className="input-group">
            <label htmlFor="totalAssets">Total Assets (₹)</label>
            <input
              type="number"
              id="totalAssets"
              value={totals.totalAssets || ""}
              onChange={(e) =>
                handleInputChange(
                  "totalAssets",
                  parseFloat(e.target.value) || 0
                )
              }
              placeholder="Enter total assets amount"
            />
          </div>

          <div className="input-group">
            <label htmlFor="totalLiabilities">Total Liabilities (₹)</label>
            <input
              type="number"
              id="totalLiabilities"
              value={totals.totalLiabilities || ""}
              onChange={(e) =>
                handleInputChange(
                  "totalLiabilities",
                  parseFloat(e.target.value) || 0
                )
              }
              placeholder="Enter total liabilities amount"
            />
          </div>
        </div>

        <div
          className={`balance-check ${isBalanced ? "balanced" : "unbalanced"}`}
        >
          <div className="balance-icon">{isBalanced ? "✅" : "⚠️"}</div>
          <div className="balance-text">
            {isBalanced
              ? "Perfect! Your balance sheet is balanced."
              : "Note: Assets and Liabilities should be equal in a balanced sheet."}
          </div>
          <div className="balance-amounts">
            <span>Assets: ₹{totals.totalAssets.toLocaleString("en-IN")}</span>
            <span>
              Liabilities: ₹{totals.totalLiabilities.toLocaleString("en-IN")}
            </span>
            <span>
              Difference: ₹
              {Math.abs(
                totals.totalAssets - totals.totalLiabilities
              ).toLocaleString("en-IN")}
            </span>
          </div>
        </div>

        {generatedBalanceSheet && (
          <div className="balance-sheet-preview">
            <div className="preview-header">
              <h3>🎯 Generated Optimal Balance Sheet</h3>
              <div className="financial-ratios">
                {(() => {
                  const ratios = calculateFinancialRatios(
                    generatedBalanceSheet
                  );
                  return (
                    <div className="ratios-grid">
                      <div className="ratio-item">
                        <span className="ratio-label">Current Ratio:</span>
                        <span className="ratio-value good">
                          {ratios.currentRatio}
                        </span>
                      </div>
                      <div className="ratio-item">
                        <span className="ratio-label">Working Capital:</span>
                        <span className="ratio-value">
                          ₹{ratios.workingCapital}
                        </span>
                      </div>
                      <div className="ratio-item">
                        <span className="ratio-label">Equity Ratio:</span>
                        <span className="ratio-value good">
                          {ratios.equityRatio}
                        </span>
                      </div>
                      <div className="ratio-item">
                        <span className="ratio-label">Debt-to-Equity:</span>
                        <span className="ratio-value good">
                          {ratios.debtToEquityRatio}
                        </span>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>

            <div className="preview-summary">
              <div className="summary-item">
                <span className="summary-label">Total Assets:</span>
                <span className="summary-value">
                  ₹
                  {(
                    Object.values(
                      generatedBalanceSheet.assets.currentAssets
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(
                      generatedBalanceSheet.assets.fixedAssets
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(
                      generatedBalanceSheet.assets.nonCurrentAssets
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(generatedBalanceSheet.assets.others).reduce(
                      (sum, val) => sum + val,
                      0
                    )
                  ).toLocaleString("en-IN")}
                </span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Total Liabilities:</span>
                <span className="summary-value">
                  ₹
                  {(
                    Object.values(
                      generatedBalanceSheet.liabilities.currentLiabilities
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(
                      generatedBalanceSheet.liabilities.branchSisterConcerns
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(
                      generatedBalanceSheet.liabilities.longTermLiabilities
                    ).reduce((sum, val) => sum + val, 0) +
                    Object.values(
                      generatedBalanceSheet.liabilities.capitalAndReserve
                    ).reduce((sum, val) => sum + val, 0)
                  ).toLocaleString("en-IN")}
                </span>
              </div>
            </div>

            <div className="key-highlights">
              <h4>✨ Key Features of Your Optimized Balance Sheet:</h4>
              <ul>
                <li>
                  🏦 <strong>Healthy Current Ratio</strong> - Good liquidity for
                  operations
                </li>
                <li>
                  💰 <strong>Balanced Asset Mix</strong> - 45% current assets,
                  40% fixed assets
                </li>
                <li>
                  📊 <strong>Strong Equity Base</strong> - 50% equity financing
                </li>
                <li>
                  ⚖️ <strong>Optimal Debt Structure</strong> - Manageable
                  debt-to-equity ratio
                </li>
                <li>
                  🎯 <strong>Professional Standards</strong> - Follows
                  accounting best practices
                </li>
              </ul>
            </div>
          </div>
        )}

        <div className="export-options">
          <h3>Generate Your Balance Sheet</h3>
          <div className="export-buttons">
            <button
              onClick={generatePDF}
              disabled={isGenerating || !generatedBalanceSheet}
              className="export-btn pdf-btn"
            >
              <span className="btn-icon">📄</span>
              <span className="btn-text">
                {isGenerating ? "Generating..." : "Download PDF"}
              </span>
              <span className="btn-desc">Professional printable format</span>
            </button>

            <button
              onClick={generateExcel}
              disabled={isGenerating || !generatedBalanceSheet}
              className="export-btn excel-btn"
            >
              <span className="btn-icon">📊</span>
              <span className="btn-text">
                {isGenerating ? "Generating..." : "Download Excel"}
              </span>
              <span className="btn-desc">
                Editable spreadsheet with formulas
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BalanceSheetCreator;
