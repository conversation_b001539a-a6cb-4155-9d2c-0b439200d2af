import React, { useState } from "react";
import { Check, Star, Zap, Crown, Shield, ArrowLeft } from "lucide-react";
import "./Pricing.css";

const Pricing = ({ onBack, onSelectPlan, onContact }) => {
  const [billingCycle, setBillingCycle] = useState("monthly");

  const handlePlanSelect = (planName, planType = "regular") => {
    if (onSelectPlan) {
      onSelectPlan(planName, planType);
    } else {
      // Default behavior - show alert for demo
      alert(`Selected: ${planName}. This would redirect to payment/signup.`);
    }
  };

  const handleContactSales = () => {
    if (onContact) {
      onContact();
    } else {
      alert(
        "Contact Sales: This would open a contact form or redirect to sales team."
      );
    }
  };

  const plans = [
    {
      name: "Free Trial",
      icon: <Zap size={24} />,
      price: { monthly: 0, yearly: 0 },
      duration: "1 Day",
      description: "Perfect for trying our platform",
      features: [
        "Single Balance Sheet Analysis",
        "Basic Financial Ratios",
        "PDF Export",
        "Email Support",
        "24-hour Access",
      ],
      limitations: ["Limited to 1 analysis", "Basic templates only"],
      buttonText: "Start Free Trial",
      popular: false,
      color: "blue",
    },
    {
      name: "Professional",
      icon: <Star size={24} />,
      price: { monthly: 29, yearly: 290 },
      duration: "per month",
      description: "For small businesses and professionals",
      features: [
        "Unlimited Balance Sheet Analysis",
        "Advanced Financial Ratios",
        "Multi-year Data Entry",
        "PDF & Excel Export",
        "Working Capital Analysis",
        "P&L Analysis",
        "Priority Email Support",
        "Custom Templates",
      ],
      buttonText: "Choose Professional",
      popular: true,
      color: "purple",
    },
    {
      name: "Enterprise",
      icon: <Crown size={24} />,
      price: { monthly: 99, yearly: 990 },
      duration: "per month",
      description: "For large organizations and teams",
      features: [
        "Everything in Professional",
        "Team Collaboration",
        "Advanced Analytics Dashboard",
        "API Access",
        "Custom Integrations",
        "Dedicated Account Manager",
        "Phone Support",
        "Custom Branding",
        "Advanced Security Features",
      ],
      buttonText: "Contact Sales",
      popular: false,
      color: "gold",
    },
  ];

  const lifetimeOffer = {
    name: "Lifetime Access",
    icon: <Shield size={24} />,
    originalPrice: 2999,
    price: 100,
    description: "Limited time launch offer - Pay once, use forever!",
    features: [
      "All Professional Features",
      "Lifetime Updates",
      "Priority Support",
      "No Monthly Fees",
      "Early Access to New Features",
      "Exclusive Community Access",
    ],
    buttonText: "Claim Lifetime Deal",
    savings: "97% OFF",
    timeLeft: "72 hours left!",
  };

  return (
    <div className="pricing-container">
      <div className="pricing-hero">
        <div className="pricing-hero-content">
          <button onClick={onBack} className="back-button">
            <ArrowLeft size={20} />
            Back to Home
          </button>
          <h1>Choose Your Plan</h1>
          <p>Start with our free trial and upgrade when you're ready</p>

          <div className="billing-toggle">
            <button
              className={billingCycle === "monthly" ? "active" : ""}
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </button>
            <button
              className={billingCycle === "yearly" ? "active" : ""}
              onClick={() => setBillingCycle("yearly")}
            >
              Yearly
              <span className="discount-badge">Save 17%</span>
            </button>
          </div>
        </div>
      </div>

      {/* Lifetime Offer Banner */}
      <div className="lifetime-offer-banner">
        <div className="lifetime-offer-content">
          <div className="offer-badge">
            <span className="savings">{lifetimeOffer.savings}</span>
            <span className="time-left">{lifetimeOffer.timeLeft}</span>
          </div>
          <div className="offer-details">
            <div className="offer-icon">{lifetimeOffer.icon}</div>
            <div className="offer-text">
              <h3>{lifetimeOffer.name}</h3>
              <p>{lifetimeOffer.description}</p>
            </div>
            <div className="offer-pricing">
              <span className="original-price">
                ${lifetimeOffer.originalPrice}
              </span>
              <span className="offer-price">${lifetimeOffer.price}</span>
            </div>
            <button
              className="lifetime-btn"
              onClick={() => handlePlanSelect("Lifetime Access", "lifetime")}
            >
              {lifetimeOffer.buttonText}
            </button>
          </div>
        </div>
      </div>

      <div className="pricing-plans">
        {plans.map((plan, index) => (
          <div
            key={index}
            className={`pricing-card ${plan.popular ? "popular" : ""} ${
              plan.color
            }`}
          >
            {plan.popular && <div className="popular-badge">Most Popular</div>}

            <div className="plan-header">
              <div className="plan-icon">{plan.icon}</div>
              <h3>{plan.name}</h3>
              <p>{plan.description}</p>
            </div>

            <div className="plan-pricing">
              <div className="price">
                <span className="currency">$</span>
                <span className="amount">
                  {billingCycle === "yearly"
                    ? plan.price.yearly
                    : plan.price.monthly}
                </span>
                <span className="duration">
                  {plan.name === "Free Trial"
                    ? plan.duration
                    : `/${billingCycle === "yearly" ? "year" : "month"}`}
                </span>
              </div>
              {billingCycle === "yearly" && plan.price.yearly > 0 && (
                <div className="yearly-savings">
                  Save ${plan.price.monthly * 12 - plan.price.yearly} per year
                </div>
              )}
            </div>

            <div className="plan-features">
              <ul>
                {plan.features.map((feature, idx) => (
                  <li key={idx}>
                    <Check size={16} />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>

              {plan.limitations && (
                <div className="plan-limitations">
                  <h4>Limitations:</h4>
                  <ul>
                    {plan.limitations.map((limitation, idx) => (
                      <li key={idx}>{limitation}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <button
              className={`plan-button ${plan.color}`}
              onClick={() => {
                if (plan.buttonText === "Contact Sales") {
                  handleContactSales();
                } else {
                  handlePlanSelect(plan.name, "plan");
                }
              }}
            >
              {plan.buttonText}
            </button>
          </div>
        ))}
      </div>

      <div className="pricing-faq">
        <h2>Frequently Asked Questions</h2>
        <div className="faq-grid">
          <div className="faq-item">
            <h3>Can I change plans anytime?</h3>
            <p>
              Yes, you can upgrade or downgrade your plan at any time. Changes
              take effect immediately.
            </p>
          </div>
          <div className="faq-item">
            <h3>Is there a setup fee?</h3>
            <p>No setup fees. You only pay for your chosen plan.</p>
          </div>
          <div className="faq-item">
            <h3>What payment methods do you accept?</h3>
            <p>
              We accept all major credit cards, PayPal, and bank transfers for
              enterprise plans.
            </p>
          </div>
          <div className="faq-item">
            <h3>Is my data secure?</h3>
            <p>
              Yes, we use bank-grade encryption and security measures to protect
              your financial data.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
