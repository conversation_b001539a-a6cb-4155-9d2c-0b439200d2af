import { User, TEST_USER_CREDENTIALS, TEST_USER_PROFILE } from '../types/Auth';

/**
 * Utility functions for test user management
 */

/**
 * Check if the current user is the test user
 */
export const isTestUser = (user: User | null): boolean => {
  if (!user) return false;
  return user.id === TEST_USER_PROFILE.id || user.email === TEST_USER_CREDENTIALS.email;
};

/**
 * Check if user has unlimited access (test user or lifetime subscription)
 */
export const hasUnlimitedAccess = (user: User | null): boolean => {
  if (!user) return false;
  
  // Test user always has unlimited access
  if (isTestUser(user)) return true;
  
  // Lifetime subscription users have unlimited access
  if (user.subscription.type === 'lifetime' && user.subscription.status === 'active') {
    return true;
  }
  
  // Super admin role has unlimited access
  if (user.role === 'super_admin') return true;
  
  return false;
};

/**
 * Check if user can export to Excel
 */
export const canExportToExcel = (user: User | null): boolean => {
  if (!user) return false;
  
  // Test user can always export
  if (isTestUser(user)) return true;
  
  return user.subscription.features.exportToExcel;
};

/**
 * Check if user can perform analysis (considering usage limits)
 */
export const canPerformAnalysis = (user: User | null): boolean => {
  if (!user) return false;
  
  // Test user can always perform analysis
  if (isTestUser(user)) return true;
  
  // Check if subscription is active
  if (user.subscription.status !== 'active') return false;
  
  // Check usage limits
  const { maxUsage, usageCount } = user.subscription;
  
  // Unlimited usage (-1)
  if (maxUsage === -1) return true;
  
  // Check if under limit
  return usageCount < maxUsage;
};

/**
 * Get remaining analyses for user
 */
export const getRemainingAnalyses = (user: User | null): number => {
  if (!user) return 0;
  
  // Test user has unlimited
  if (isTestUser(user)) return -1;
  
  const { maxUsage, usageCount } = user.subscription;
  
  // Unlimited usage
  if (maxUsage === -1) return -1;
  
  return Math.max(0, maxUsage - usageCount);
};

/**
 * Get user display name
 */
export const getUserDisplayName = (user: User | null): string => {
  if (!user) return 'Guest';
  
  if (isTestUser(user)) {
    return `${user.firstName} ${user.lastName} (Test User)`;
  }
  
  return `${user.firstName} ${user.lastName}`;
};

/**
 * Get subscription display info
 */
export const getSubscriptionDisplayInfo = (user: User | null): {
  planName: string;
  status: string;
  isTestUser: boolean;
} => {
  if (!user) {
    return { planName: 'None', status: 'inactive', isTestUser: false };
  }
  
  const testUser = isTestUser(user);
  
  if (testUser) {
    return {
      planName: 'Test Account (Full Access)',
      status: 'active',
      isTestUser: true
    };
  }
  
  const planNames = {
    free: 'Free Trial',
    premium: 'Premium',
    lifetime: 'Lifetime Access'
  };
  
  return {
    planName: planNames[user.subscription.type] || 'Unknown',
    status: user.subscription.status,
    isTestUser: false
  };
};

/**
 * Log test user activity (for debugging)
 */
export const logTestUserActivity = (action: string, user: User | null): void => {
  if (isTestUser(user)) {
    console.log(`🧪 Test User Activity: ${action}`, {
      user: user?.email,
      timestamp: new Date().toISOString()
    });
  }
};
